# SinglePicFragment 保存方法暴露功能文档

## 概述

已成功为 `SinglePicFragment` 暴露保存方法，让 `SavePicActivity` 能够调用并获取当前的相纸规格设置。这个实现提供了更好的架构分离和功能封装。

## 功能特性

### 1. Fragment 保存方法暴露
- **统一保存接口**: `saveCurrentImage(SaveCallback callback)`
- **智能保存策略**: 根据图片类型自动选择保存方式
- **相纸规格感知**: 自动使用当前选择的相纸尺寸和裁剪线设置

### 2. Activity 调用优化
- **Fragment 获取**: 智能获取当前活动的 Fragment
- **回调处理**: 完整的成功/失败回调机制
- **备用方案**: Fragment 保存失败时的优雅降级

### 3. 文件命名优化
- **时间戳**: 使用时间戳避免文件名冲突
- **规格标识**: 文件名包含相纸尺寸和裁剪线信息
- **类型区分**: 单张照片和排版照片的明确标识

## 技术实现

### 1. SinglePicFragment 新增方法

#### 主要保存方法
```java
public void saveCurrentImage(SaveCallback callback) {
    if (destBitmap == null) {
        if (callback != null) {
            callback.onError("没有可保存的图片");
        }
        return;
    }
    
    if (saveType == 2) {
        // 排版照片保存
        saveLayoutPhotoWithCurrentSettings(callback);
    } else {
        // 单张照片保存
        saveSinglePhoto(callback);
    }
}
```

#### 排版照片保存
```java
private void saveLayoutPhotoWithCurrentSettings(SaveCallback callback) {
    if (ApiConfig.PAPER_SIZE_6_INCH.equals(currentPaperSize)) {
        // 6寸专用方法，支持裁剪线
        IdPhotoManager.getInstance().generate6InchLayoutPhoto(
            filePath, picSize, withCropLine, callback);
    } else {
        // 通用方法，支持其他相纸尺寸
        IdPhotoManager.getInstance().generateLayoutPhotoWithPaperSize(
            filePath, picSize, currentPaperSize, callback);
    }
}
```

#### 文件保存处理
```java
private void saveBitmapToFile(Bitmap bitmap, String fileName, SaveCallback callback) {
    try {
        File picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);
        File appDir = new File(picturesDir, "证件照");
        if (!appDir.exists()) {
            appDir.mkdirs();
        }
        
        File imageFile = new File(appDir, fileName);
        FileOutputStream fos = new FileOutputStream(imageFile);
        bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos);
        fos.flush();
        fos.close();
        
        // 通知系统媒体库更新
        Intent mediaScanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
        Uri contentUri = Uri.fromFile(imageFile);
        mediaScanIntent.setData(contentUri);
        requireContext().sendBroadcast(mediaScanIntent);
        
        if (callback != null) {
            callback.onSuccess(imageFile.getAbsolutePath());
        }
    } catch (IOException e) {
        if (callback != null) {
            callback.onError("保存文件失败: " + e.getMessage());
        }
    }
}
```

#### 智能文件命名
```java
private String generateFileName(String type, String paperSize, boolean withCropLine) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
    String timestamp = sdf.format(new Date());
    
    StringBuilder fileName = new StringBuilder();
    fileName.append(type).append("_").append(timestamp);
    
    if (paperSize != null) {
        fileName.append("_").append(paperSize.replace(" ", ""));
    }
    
    if (withCropLine) {
        fileName.append("_裁剪线");
    }
    
    fileName.append(".jpg");
    return fileName.toString();
}
```

#### 回调接口定义
```java
public interface SaveCallback {
    void onStart();
    void onSuccess(String filePath);
    void onError(String error);
}
```

### 2. SavePicActivity 调用优化

#### 排版照片保存
```java
private void saveLayoutPhoto() {
    // 获取当前排版照Fragment并调用其保存方法
    SinglePicFragment layoutFragment = getCurrentLayoutFragment();
    if (layoutFragment != null) {
        showLoadingDialog();
        layoutFragment.saveCurrentImage(new SinglePicFragment.SaveCallback() {
            @Override
            public void onStart() {
                // 已经显示了加载对话框
            }

            @Override
            public void onSuccess(String filePath) {
                if (loadingDialog != null) {
                    loadingDialog.dismiss();
                }
                ToastUtil.showToast(SavePicActivity.this, "保存成功: " + filePath);
            }

            @Override
            public void onError(String error) {
                if (loadingDialog != null) {
                    loadingDialog.dismiss();
                }
                ToastUtil.showToast(SavePicActivity.this, "保存失败: " + error);
                // 如果Fragment保存失败，尝试使用原有的备用方案
                generateLayoutWithNewService();
            }
        });
    } else {
        // 如果无法获取Fragment，使用默认设置
        generateLayoutWithNewService();
    }
}
```

#### Fragment 获取方法
```java
private SinglePicFragment getCurrentLayoutFragment() {
    try {
        if (viewPager != null && viewPager.getAdapter() instanceof FragmentPagerAdapter) {
            FragmentPagerAdapter adapter = (FragmentPagerAdapter) viewPager.getAdapter();
            // 排版照通常是第二个tab (index=1)
            Fragment fragment = adapter.getItem(1);
            if (fragment instanceof SinglePicFragment) {
                return (SinglePicFragment) fragment;
            }
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
    return null;
}

private SinglePicFragment getCurrentSingleFragment() {
    try {
        if (viewPager != null && viewPager.getAdapter() instanceof FragmentPagerAdapter) {
            FragmentPagerAdapter adapter = (FragmentPagerAdapter) viewPager.getAdapter();
            // 单张照片通常是第一个tab (index=0)
            Fragment fragment = adapter.getItem(0);
            if (fragment instanceof SinglePicFragment) {
                return (SinglePicFragment) fragment;
            }
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
    return null;
}
```

## 架构优势

### 1. 职责分离
- **Fragment**: 负责图片处理和保存逻辑
- **Activity**: 负责UI交互和流程控制
- **Service**: 负责API调用和业务逻辑

### 2. 配置感知
- **相纸尺寸**: 自动使用当前选择的相纸规格
- **裁剪线**: 自动应用裁剪线设置
- **图片类型**: 智能区分单张和排版照片

### 3. 错误处理
- **完整回调**: 提供开始、成功、失败的完整回调
- **优雅降级**: Fragment 失败时使用 Activity 备用方案
- **用户反馈**: 清晰的成功/失败提示信息

### 4. 文件管理
- **统一目录**: 所有照片保存到统一的"证件照"目录
- **媒体库更新**: 自动通知系统更新媒体库
- **文件命名**: 智能的文件命名规则

## 文件命名规则

### 1. 单张照片
```
single_20231228_143052.jpg
```

### 2. 排版照片（6寸）
```
layout_20231228_143052_6inch.jpg
```

### 3. 排版照片（6寸带裁剪线）
```
layout_20231228_143052_6inch_裁剪线.jpg
```

### 4. 排版照片（A4）
```
layout_20231228_143052_A4.jpg
```

## 使用示例

### Fragment 中调用
```java
// 在 Fragment 中保存当前图片
saveCurrentImage(new SaveCallback() {
    @Override
    public void onStart() {
        // 显示加载状态
    }
    
    @Override
    public void onSuccess(String filePath) {
        // 保存成功，filePath 是保存的文件路径
        Toast.makeText(getContext(), "保存成功: " + filePath, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onError(String error) {
        // 保存失败
        Toast.makeText(getContext(), "保存失败: " + error, Toast.LENGTH_SHORT).show();
    }
});
```

### Activity 中调用
```java
// 在 Activity 中调用 Fragment 的保存方法
SinglePicFragment fragment = getCurrentLayoutFragment();
if (fragment != null) {
    fragment.saveCurrentImage(callback);
}
```

## 测试建议

### 1. 功能测试
- 测试单张照片保存
- 测试不同相纸尺寸的排版照片保存
- 测试带裁剪线的排版照片保存
- 验证文件命名规则

### 2. 异常测试
- 测试存储空间不足的情况
- 测试权限被拒绝的情况
- 测试 Fragment 为空的情况
- 测试网络异常的情况

### 3. 用户体验测试
- 验证加载状态显示
- 验证成功/失败提示
- 验证文件保存位置
- 验证媒体库更新

## 注意事项

1. **权限管理**: 确保应用有存储权限
2. **线程安全**: 文件操作在适当的线程中执行
3. **内存管理**: 及时释放 Bitmap 资源
4. **错误处理**: 完善的异常捕获和处理
5. **用户反馈**: 清晰的操作结果提示

## 总结

通过暴露 `SinglePicFragment` 的保存方法，我们实现了：

- ✅ **更好的架构**: Fragment 负责具体保存逻辑
- ✅ **配置感知**: 自动使用当前的相纸规格设置
- ✅ **智能命名**: 包含规格信息的文件命名
- ✅ **完整回调**: 提供完整的保存状态反馈
- ✅ **优雅降级**: 失败时的备用方案
- ✅ **用户友好**: 清晰的成功/失败提示

这个实现提供了更好的代码组织和用户体验，同时保持了系统的稳定性和可维护性。
