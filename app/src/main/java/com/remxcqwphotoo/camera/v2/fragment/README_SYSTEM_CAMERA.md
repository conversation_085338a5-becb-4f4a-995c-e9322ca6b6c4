# PhotoFragment 系统相机集成

## 概述

已成功将 `PhotoFragment.java` 中的拍照功能从使用自定义 `CameraActivity` 调整为使用系统相机，提供更好的兼容性和用户体验。

## 主要修改

### 1. 导入新增
```java
import android.app.Activity;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import androidx.core.content.FileProvider;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
```

### 2. 新增常量和变量
```java
// 系统相机相关常量
private static final int REQUEST_SYSTEM_CAMERA = 203;
private Uri currentPhotoUri;
```

### 3. 核心方法实现

#### startCameraActivity() 重构
```java
private void startCameraActivity() {
    startSystemCamera();
}
```

#### 系统相机启动
```java
private void startSystemCamera() {
    Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
    
    // 确保有相机应用可以处理这个intent
    if (takePictureIntent.resolveActivity(requireActivity().getPackageManager()) != null) {
        // 创建图片文件
        File photoFile = createImageFile();
        
        if (photoFile != null) {
            currentPhotoUri = FileProvider.getUriForFile(requireContext(),
                    "com.remxcqwphotoo.camera.fileprovider",
                    photoFile);
            takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, currentPhotoUri);
            startActivityForResult(takePictureIntent, REQUEST_SYSTEM_CAMERA);
        }
    }
}
```

#### 图片文件创建
```java
private File createImageFile() throws IOException {
    String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
    String imageFileName = "JPEG_" + timeStamp + "_";
    File storageDir = requireActivity().getExternalFilesDir(Environment.DIRECTORY_PICTURES);
    
    return File.createTempFile(imageFileName, ".jpg", storageDir);
}
```

#### 结果处理
```java
@Override
public void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    
    if (requestCode == REQUEST_SYSTEM_CAMERA && resultCode == Activity.RESULT_OK) {
        if (currentPhotoUri != null) {
            String photoPath = getRealPathFromUri(currentPhotoUri);
            if (photoPath != null) {
                performFaceDetection(photoPath);
            }
        }
    }
}
```

#### 路径解析
```java
private String getRealPathFromUri(Uri uri) {
    try {
        String uriString = uri.toString();
        if (uriString.contains("external_files")) {
            String fileName = uriString.substring(uriString.lastIndexOf("/") + 1);
            File storageDir = requireActivity().getExternalFilesDir(Environment.DIRECTORY_PICTURES);
            File actualFile = new File(storageDir, fileName);
            
            if (actualFile.exists()) {
                return actualFile.getAbsolutePath();
            }
        }
        return null;
    } catch (Exception e) {
        return null;
    }
}
```

#### 人脸检测集成
```java
private void performFaceDetection(String imagePath) {
    new FaceDetectManager(getActivity(), imagePath).startWithFallback(new FaceDetectManager.DetectBack() {
        @Override
        public void onSuccess(String path) {
            openPreview(path);
        }

        @Override
        public void onFail(String msg) {
            if (!TextUtils.isEmpty(msg)) {
                ToastUtil.showToast(getActivity(), msg);
            }
        }
    });
}
```

### 4. FileProvider 配置

#### file_paths_public.xml 更新
```xml
<paths>
    <external-path
        name="my_images"
        path="Pictures"/>
    <external-cache-path
        name="share_cache"
        path="share"/>
    <external-files-path
        name="external_files"
        path="Pictures"/>
</paths>
```

## 技术优势

### 1. 兼容性提升
- **系统相机**: 使用设备原生相机应用，兼容性更好
- **无需自定义**: 避免自定义相机可能出现的兼容性问题
- **权限简化**: 减少相机权限管理复杂度

### 2. 用户体验改善
- **熟悉界面**: 用户使用熟悉的系统相机界面
- **功能完整**: 享受系统相机的所有功能（闪光灯、对焦等）
- **性能稳定**: 系统相机经过充分测试，稳定性更好

### 3. 维护成本降低
- **代码简化**: 无需维护复杂的自定义相机逻辑
- **更新跟随**: 系统相机功能随系统更新自动改善
- **问题减少**: 减少相机相关的bug和兼容性问题

## 工作流程

### 1. 拍照流程
```
用户点击拍照 → 启动系统相机 → 用户拍照 → 保存到应用目录 → 返回图片路径
```

### 2. 处理流程
```
获取图片路径 → 人脸检测（带fallback） → 检测成功 → 打开预览界面
```

### 3. 文件管理
```
系统相机 → FileProvider → 应用私有目录 → 人脸检测 → 后续处理
```

## 安全性考虑

### 1. 文件权限
- 使用 `FileProvider` 安全地共享文件
- 图片保存在应用私有目录
- 避免直接暴露文件路径

### 2. 权限管理
- 系统相机自动处理相机权限
- 应用只需要存储权限
- 减少权限申请复杂度

## 错误处理

### 1. 相机不可用
```java
if (takePictureIntent.resolveActivity(requireActivity().getPackageManager()) != null) {
    // 启动相机
} else {
    ToastUtil.showToast(getActivity(), "没有找到相机应用");
}
```

### 2. 文件创建失败
```java
try {
    photoFile = createImageFile();
} catch (IOException ex) {
    ToastUtil.showToast(getActivity(), "创建图片文件失败");
    return;
}
```

### 3. 路径解析失败
```java
String photoPath = getRealPathFromUri(currentPhotoUri);
if (photoPath != null) {
    performFaceDetection(photoPath);
} else {
    ToastUtil.showToast(getActivity(), "获取图片路径失败");
}
```

## 测试建议

### 1. 功能测试
- 测试拍照功能是否正常
- 验证图片保存位置
- 检查人脸检测是否正常工作

### 2. 兼容性测试
- 不同Android版本测试
- 不同设备厂商测试
- 有无相机应用的设备测试

### 3. 异常测试
- 存储空间不足
- 相机权限被拒绝
- 相机应用崩溃

## 注意事项

1. **FileProvider配置**: 确保 `file_paths_public.xml` 正确配置
2. **权限声明**: 确保 `AndroidManifest.xml` 中有相机和存储权限
3. **文件清理**: 考虑定期清理临时图片文件
4. **内存管理**: 及时释放大图片资源

## 未来扩展

1. **图片质量控制**: 可以通过Intent参数控制图片质量
2. **多张拍照**: 支持连续拍摄多张照片
3. **视频录制**: 扩展支持视频录制功能
4. **自定义设置**: 传递更多参数给系统相机
