package com.remxcqwphotoo.camera.v2.fragment;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import androidx.core.content.PermissionChecker;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction;
import com.remxcqwphotoo.camera.R;

import com.remxcqwphotoo.camera.CameraActivity;
import com.remxcqwphotoo.camera.Const;
import com.remxcqwphotoo.camera.PicturePreviewActivity;
import com.remxcqwphotoo.camera.ad.AdSdk;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.net.DetectTask;
import com.remxcqwphotoo.camera.utils.GlideEngine;
import com.remxcqwphotoo.camera.v2.GuideActivity;
import com.remxcqwphotoo.camera.v2.SpecListActivity;
import com.remxcqwphotoo.camera.v2.base.BaseFragment;
import com.remxcqwphotoo.camera.v2.fragment.fav.SizeListFragment;
import com.remxcqwphotoo.camera.v2.fragment.fav.TakePhotoSkillsFragment;
import com.remxcqwphotoo.camera.v2.util.ToastUtil;

import com.qmuiteam.qmui.widget.dialog.QMUIBottomSheet;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import butterknife.ButterKnife;

public class PhotoFragment extends BaseFragment {
    private PicSize selectSize = null;

    // Permission Request Codes (Fragment specific)
    private static final int REQUEST_CAMERA_PERMISSION_FRAGMENT = 201;
    private static final int REQUEST_STORAGE_PERMISSION_FRAGMENT = 202;


    @Override
    protected View onCreateView() {
        FrameLayout layout = (FrameLayout) LayoutInflater.from(getActivity()).inflate(R.layout.fragment_photo, null);

        ButterKnife.bind(this, layout);
        LinearLayout content = layout.findViewById(R.id.content);
        layout.findViewById(R.id.top1).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startFragment(SizeListFragment.newInstance(SizeListFragment.SIGN));
            }
        });
        layout.findViewById(R.id.top2).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), SpecListActivity.class);
                intent.putExtra(SpecListActivity.ENTER_TYPE, 0);
                startActivity(intent);
            }
        });
        FrameLayout adContainer = layout.findViewById(R.id.ad_container);
        AdSdk.getInstance().showBanner(getActivity(), adContainer);
        layout.findViewById(R.id.top3).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startFragment(new TakePhotoSkillsFragment());

            }
        });
        layout.findViewById(R.id.section_view).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), SpecListActivity.class);
                startActivity(intent);
            }
        });
        layout.findViewById(R.id.iv_cell1).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(0);
                showChoiceDialog();

            }
        });
        layout.findViewById(R.id.iv_cell2).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(2);
                showChoiceDialog();

            }
        });
        layout.findViewById(R.id.iv_cell3).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(0);
                showChoiceDialog();
            }
        });
        layout.findViewById(R.id.iv_cell4).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(1);
                showChoiceDialog();

            }
        });
        layout.findViewById(R.id.iv_cell5).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(3);
                showChoiceDialog();
            }
        });
        layout.findViewById(R.id.iv_cell6).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(2);
                showChoiceDialog();
            }
        });
        layout.findViewById(R.id.iv_cell7).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(7);
                showChoiceDialog();

            }
        });
        layout.findViewById(R.id.iv_cell8).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(2);
                showChoiceDialog();
            }
        });
        layout.findViewById(R.id.iv_cell9).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectSize = Const.allPicList.get(0);
                showChoiceDialog();
            }
        });
        layout.findViewById(R.id.banner).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), GuideActivity.class);
                startActivity(intent);
            }
        });
        return layout;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }


    private void showChoiceDialog() {
        QMUIBottomSheet bottomSheet = new QMUIBottomSheet(getContext());
        bottomSheet.setContentView(R.layout.custom_dialog_layout);
        bottomSheet.show();
        bottomSheet.findViewById(R.id.close_indicator).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomSheet.dismiss();
            }
        });
        bottomSheet.findViewById(R.id.btn_photo).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openAlbum();
            }
        });
        bottomSheet.findViewById(R.id.btn_camera).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openCamera();
            }
        });
    }

    public void openPreview(String path) {
        Intent intent = new Intent(getActivity(), PicturePreviewActivity.class);
        intent.putExtra(PicturePreviewActivity.PICK_IMG, path);
        intent.putExtra("SIZE", selectSize);
        startActivity(intent);
    }

    private void openCamera() {
        if(ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED){
            startCameraActivity();
        }else {
            new QMUIDialog.MessageDialogBuilder(getActivity()).setMessage("此功能需要相机权限拍照,是否允许获取相机权限?").addAction(new QMUIDialogAction(getActivity(), "取消", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                }
            })).addAction(new QMUIDialogAction(getActivity(), "确定", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                    checkAndRequestCameraPermission();
                }
            })).create().show();
        }

    }

    private void checkAndRequestCameraPermission() {
        if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            startCameraActivity();
        } else {
            // Consider showing a rationale before requesting? (Optional)
            // shouldShowRequestPermissionRationale(...)
            requestPermissions(new String[]{Manifest.permission.CAMERA}, REQUEST_CAMERA_PERMISSION_FRAGMENT);
        }
    }

    private void startCameraActivity() {
        Intent intent = new Intent(getActivity(), CameraActivity.class);
        intent.putExtra("SIZE", selectSize);
        startActivity(intent);
    }


    private void openAlbum() {
        String permissionNeeded = getStoragePermission();

        if (ContextCompat.checkSelfPermission(requireContext(), permissionNeeded) == PackageManager.PERMISSION_GRANTED) {
            openGallery();
        } else {
            String permsTips = "";
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                permsTips = "选择图片需要获取您存储权限是否允许？";
            } else {
                permsTips = "选择图片需要获取您的读取相册权限是否允许？";
            }
            new QMUIDialog.MessageDialogBuilder(getActivity()).setMessage(permsTips).addAction(new QMUIDialogAction(getActivity(), "不允许", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                }
            })).addAction(new QMUIDialogAction(getActivity(), "允许", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                    requestStoragePermission();
                }
            })).create().show();
        }
    }

    private String getStoragePermission() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ?
                Manifest.permission.READ_MEDIA_IMAGES : Manifest.permission.READ_EXTERNAL_STORAGE;
    }

    private void requestStoragePermission() {
        String permissionNeeded = getStoragePermission();
        // Consider showing a rationale before requesting? (Optional)
        // shouldShowRequestPermissionRationale(...)
        requestPermissions(new String[]{permissionNeeded}, REQUEST_STORAGE_PERMISSION_FRAGMENT);
    }


    private void openGallery() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage()).setMaxSelectNum(1).setImageEngine(GlideEngine.Companion.createGlideEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {

                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String picPath = result.get(0).getRealPath();
                        new DetectTask(getActivity(), picPath).startWithFallback(new DetectTask.DetectBack() {
                            @Override
                            public void onSuccess(String path) {
                                openPreview(path);
                            }

                            @Override
                            public void onFail(String msg) {
                                if (!TextUtils.isEmpty(msg)) {
                                    ToastUtil.showToast(getActivity(), msg);
                                }
                            }
                        });
                    }

                    @Override
                    public void onCancel() {
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case REQUEST_CAMERA_PERMISSION_FRAGMENT:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startCameraActivity();
                } else {
                    // Permission Denied
                    ToastUtils.showLong("请打开相机权限");
                    // Optionally, guide user to settings
                    // AppUtils.launchAppDetailsSettings();
                }
                break;
            case REQUEST_STORAGE_PERMISSION_FRAGMENT:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openGallery();
                } else {
                    // Permission Denied
                    ToastUtils.showLong("请允许获取相关授权");
                    // Optionally, guide user to settings
                    // AppUtils.launchAppDetailsSettings();
                }
                break;
        }
    }
}
