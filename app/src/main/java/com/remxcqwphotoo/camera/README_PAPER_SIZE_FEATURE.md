# 排版照相纸尺寸切换功能实现文档

## 概述

已成功为排版照 Tab 页新增相纸尺寸切换功能，并更新人脸检测使用新的API。用户现在可以在排版照预览时选择不同的相纸尺寸，保存时会根据选择的相纸规格进行保存。

## 功能特性

### 1. 相纸尺寸选择
- **6寸**: 1205x1795 (默认选择)
- **5寸**: 1051x1500
- **A4**: 2479x3508
- **3R**: 1051x1500
- **4R**: 1205x1795

### 2. 裁剪线开关
- 支持开启/关闭裁剪线
- 仅在6寸相纸时生效
- 实时预览效果

### 3. 实时预览
- 切换相纸尺寸时自动重新生成预览
- 切换裁剪线时自动更新预览
- 保持90度旋转显示效果

## 技术实现

### 1. UI界面更新

#### fragment_layout_single_pic.xml
```xml
<!-- 相纸尺寸选择区域 -->
<LinearLayout
    android:id="@+id/paper_size_selector"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="#ffffff"
    android:gravity="center_vertical">

    <TextView android:text="相纸尺寸：" />
    
    <QMUITabSegment
        android:id="@+id/paper_size_tabs"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_weight="1" />

    <TextView android:text="裁剪线" />
    
    <Switch android:id="@+id/crop_line_switch" />

</LinearLayout>
```

### 2. SinglePicFragment.java 增强

#### 新增成员变量
```java
private QMUITabSegment paperSizeTabs;
private Switch cropLineSwitch;
private String currentPaperSize = ApiConfig.DEFAULT_PAPER_SIZE;
private boolean withCropLine = false;
```

#### 相纸尺寸选择器初始化
```java
private void initPaperSizeSelector(View view) {
    paperSizeTabs = view.findViewById(R.id.paper_size_tabs);
    cropLineSwitch = view.findViewById(R.id.crop_line_switch);
    
    // 添加Tab选项
    paperSizeTabs.addTab(new QMUITabSegment.Tab("6寸"));
    paperSizeTabs.addTab(new QMUITabSegment.Tab("5寸"));
    paperSizeTabs.addTab(new QMUITabSegment.Tab("A4"));
    paperSizeTabs.addTab(new QMUITabSegment.Tab("3R"));
    paperSizeTabs.addTab(new QMUITabSegment.Tab("4R"));
    
    // 设置监听器
    paperSizeTabs.addOnTabSelectedListener(new OnTabSelectedListener() {
        // 处理相纸尺寸切换
    });
    
    cropLineSwitch.setOnCheckedChangeListener(new OnCheckedChangeListener() {
        // 处理裁剪线开关
    });
}
```

#### 智能预览生成
```java
private void generatePreviewWithApi() {
    if (ApiConfig.PAPER_SIZE_6_INCH.equals(currentPaperSize)) {
        // 6寸专用方法，支持裁剪线
        IdPhotoManager.getInstance().generate6InchLayoutPhoto(
            filePath, picSize, withCropLine, callback);
    } else {
        // 通用方法，支持其他相纸尺寸
        IdPhotoManager.getInstance().generateLayoutPhotoWithPaperSize(
            filePath, picSize, currentPaperSize, callback);
    }
}
```

### 3. SavePicActivity.java 保存逻辑更新

#### 智能保存策略
```java
private void saveLayoutPhoto() {
    // 获取当前排版照Fragment的相纸设置
    SinglePicFragment layoutFragment = getCurrentLayoutFragment();
    if (layoutFragment != null) {
        String paperSize = layoutFragment.getCurrentPaperSize();
        boolean withCropLine = layoutFragment.isWithCropLine();
        generateLayoutWithSelectedPaperSize(paperSize, withCropLine);
    } else {
        // 使用默认设置
        generateLayoutWithNewService();
    }
}
```

#### 根据相纸尺寸生成
```java
private void generateLayoutWithSelectedPaperSize(String paperSize, boolean withCropLine) {
    if (ApiConfig.PAPER_SIZE_6_INCH.equals(paperSize)) {
        // 6寸带裁剪线支持
        IdPhotoManager.getInstance().generate6InchLayoutPhoto(
            filePath, picSize, withCropLine, callback);
    } else {
        // 其他相纸尺寸
        IdPhotoManager.getInstance().generateLayoutPhotoWithPaperSize(
            filePath, picSize, paperSize, callback);
    }
}
```

## 人脸检测API更新

### 1. FaceDetectManager.java 更新
```java
// 使用新的人脸检测API，默认使用MTCNN模型
FaceDetectionService.getInstance().detectFaceWithNewApi(
    filePath, 
    ApiConfig.DEFAULT_FACE_MODEL, 
    callback
);
```

### 2. 新API特性
- **多模型支持**: MTCNN, RetinaFace-ResNet50, Face++
- **增强数据**: 置信度、旋转角度、关键点信息
- **更高精度**: 更准确的人脸检测结果

## 用户体验

### 1. 界面交互
- **直观选择**: Tab形式的相纸尺寸选择
- **实时反馈**: 切换时立即更新预览
- **状态保持**: 选择状态在预览和保存间保持一致

### 2. 智能隐藏
- **条件显示**: 仅在排版照Tab页显示相纸选择器
- **单张照片**: 电子照Tab页自动隐藏选择器
- **界面简洁**: 避免不必要的UI元素

### 3. 错误处理
- **优雅降级**: API失败时使用备用方案
- **用户提示**: 清晰的错误信息反馈
- **状态恢复**: 失败后保持用户选择状态

## 技术优势

### 1. 模块化设计
- **服务分离**: 不同相纸尺寸使用专门的服务方法
- **配置统一**: 所有相纸规格在ApiConfig中统一管理
- **接口一致**: 保持统一的回调接口设计

### 2. 性能优化
- **按需生成**: 只在用户切换时重新生成预览
- **缓存复用**: 相同设置下复用已生成的图片
- **异步处理**: 所有API调用都在后台线程执行

### 3. 扩展性
- **新增相纸**: 可轻松添加新的相纸尺寸
- **功能扩展**: 可添加更多排版选项（如间距、边距等）
- **API升级**: 支持未来API功能扩展

## 配置说明

### 1. 默认设置
```java
// 默认相纸尺寸：6寸
public static final String DEFAULT_PAPER_SIZE = PAPER_SIZE_6_INCH;

// 默认人脸检测模型：MTCNN
public static final String DEFAULT_FACE_MODEL = FACE_MODEL_MTCNN;
```

### 2. 相纸尺寸映射
```java
PAPER_SIZE_6_INCH = "6 inch"    // 1205x1795
PAPER_SIZE_5_INCH = "5 inch"    // 1051x1500
PAPER_SIZE_A4 = "A4"            // 2479x3508
PAPER_SIZE_3R = "3R"            // 1051x1500
PAPER_SIZE_4R = "4R"            // 1205x1795
```

## 测试建议

### 1. 功能测试
- 测试所有相纸尺寸切换
- 验证裁剪线开关功能
- 检查保存文件的正确性

### 2. 界面测试
- 验证Tab切换的视觉效果
- 检查预览图的显示效果
- 测试旋转显示是否正确

### 3. 性能测试
- 测试切换时的响应速度
- 验证内存使用情况
- 检查网络请求的效率

## 注意事项

1. **相纸选择器**: 仅在排版照Tab页显示
2. **裁剪线功能**: 目前仅6寸相纸支持
3. **预览旋转**: 排版照保持90度旋转显示
4. **状态同步**: 预览和保存使用相同的设置
5. **错误处理**: 确保API失败时的优雅降级

## 未来扩展

1. **更多相纸规格**: 支持自定义尺寸
2. **排版选项**: 添加间距、边距等设置
3. **批量处理**: 支持多张照片批量排版
4. **模板系统**: 预设排版模板选择
