package com.remxcqwphotoo.camera.model;

import java.util.List;

/**
 * 新的人脸检测API响应数据模型
 * API: /face_detection
 */
public class NewFaceDetectData {
    /**
     * 检测状态
     */
    public boolean status;
    
    /**
     * 检测到的人脸数量
     */
    public int face_count;
    
    /**
     * 人脸信息列表
     */
    public List<Face> faces;
    
    /**
     * 图片信息
     */
    public ImageInfo image_info;
    
    /**
     * 使用的检测模型
     */
    public String model_used;
    
    /**
     * 错误信息（当status为false时）
     */
    public String error;
    
    /**
     * 人脸信息
     */
    public static class Face {
        /**
         * 人脸矩形区域
         */
        public Rectangle rectangle;
        
        /**
         * 人脸中心点
         */
        public Center center;
        
        /**
         * 人脸旋转角度
         */
        public float roll_angle;
        
        /**
         * 置信度
         */
        public float confidence;
        
        /**
         * 人脸区域面积
         */
        public int area;
        
        /**
         * 关键点信息（可选）
         */
        public Landmarks landmarks;
    }
    
    /**
     * 矩形区域
     */
    public static class Rectangle {
        public int left;
        public int top;
        public int width;
        public int height;
        
        /**
         * 获取右边界
         */
        public int getRight() {
            return left + width;
        }
        
        /**
         * 获取下边界
         */
        public int getBottom() {
            return top + height;
        }
        
        /**
         * 检查矩形是否有效
         */
        public boolean isValid() {
            return width > 0 && height > 0 && left >= 0 && top >= 0;
        }
    }
    
    /**
     * 中心点
     */
    public static class Center {
        public int x;
        public int y;
    }
    
    /**
     * 关键点信息
     */
    public static class Landmarks {
        public float[] left_eye;
        public float[] right_eye;
        public float[] nose;
        public float[] left_mouth;
        public float[] right_mouth;
    }
    
    /**
     * 图片信息
     */
    public static class ImageInfo {
        public int width;
        public int height;
        public int channels;
    }
    
    /**
     * 转换为旧的FaceDetectData格式（用于兼容性）
     */
    public FaceDetectData toFaceDetectData() {
        FaceDetectData oldData = new FaceDetectData();
        oldData.face_count = this.face_count;
        oldData.processing_time = 0.0; // 新API不提供处理时间
        
        if (this.faces != null && !this.faces.isEmpty()) {
            oldData.face_locations = new java.util.ArrayList<>();
            for (Face face : this.faces) {
                if (face.rectangle != null) {
                    FaceDetectData.FaceLocation location = new FaceDetectData.FaceLocation();
                    location.left = face.rectangle.left;
                    location.top = face.rectangle.top;
                    location.right = face.rectangle.getRight();
                    location.bottom = face.rectangle.getBottom();
                    oldData.face_locations.add(location);
                }
            }
        }
        
        return oldData;
    }
}
