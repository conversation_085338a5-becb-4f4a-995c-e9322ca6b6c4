package com.remxcqwphotoo.camera;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.ad.AdSdk;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.mgr.IdPhotoManager;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import com.qmuiteam.qmui.widget.QMUITopBar;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;

import io.reactivex.rxjava3.disposables.CompositeDisposable;
//import rxhttp.wrapper.param.RxHttp;

import static com.qmuiteam.qmui.widget.dialog.QMUITipDialog.Builder.ICON_TYPE_LOADING;


public class PicturePreviewActivity extends AppCompatActivity implements View.OnClickListener {
    public final static String PICK_IMG = "PICK_IMG";
    private String resultPath;
    private CompositeDisposable disposables = new CompositeDisposable();
    private int[] ids = {R.id.iv_white, R.id.iv_blue, R.id.iv_red};
    private ImageView cropImageView;
    private Bitmap originBitmap = null;
    private Bitmap destBitmap = null;
    private QMUITipDialog tipsDialog = null;
    int curPos = 0;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE);
        setContentView(R.layout.activity_picture_preview);
        cropImageView = findViewById(R.id.cropImageView);
        initView();
        resultPath = getIntent().getStringExtra(PICK_IMG);
        generateIdPhotoOnly();
        AdSdk.getInstance().showInterAd(this);
    }

    /**
     * 仅生成证件照（带完整fallback策略）
     */
    private void generateIdPhotoOnly() {
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");
        // 使用带fallback的证件照生成
        IdPhotoManager.getInstance().generateIdPhotoWithFallback(this, resultPath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (tipsDialog == null) {
                    tipsDialog = new QMUITipDialog.Builder(PicturePreviewActivity.this)
                            .setIconType(ICON_TYPE_LOADING)
                            .setTipWord("正在处理请耐心等待...")
                            .create();
                }
                tipsDialog.show();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {

                originBitmap = bitmap;
                cropImageView.setImageBitmap(originBitmap);
            }

            @Override
            public void onError(String error) {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();

                }
                ToastUtils.showShort("所有处理方案都失败了: " + error);
            }

            @Override
            public void onComplete() {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
                // 处理完成
            }
        });
    }

    public void drawImage(int colorPos, boolean isNext) {
        if (originBitmap == null) {
            ToastUtils.showShort("数据解析失败");
            return;
        }
        int selectColor = Color.rgb(255, 255, 255);
        if (colorPos == 1) {
            selectColor = Color.rgb(67, 142, 219);
        } else if (colorPos == 2) {
            selectColor = Color.rgb(255, 0, 0);
        }
        curPos = colorPos;
        if (!isNext) {
            cropImageView.setBackgroundColor(selectColor);
            return;
        }

        int bH = originBitmap.getHeight();
        int bW = originBitmap.getWidth();
        Bitmap canvasBitmap = Bitmap.createBitmap(bW, bH, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(canvasBitmap);
        Paint paint = new Paint();
        paint.setColor(selectColor);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawRect(0, 0, bW, bH, paint);
        canvas.drawBitmap(originBitmap, new Rect(0, 0, bW, bH), new Rect(0, 0, bW, bH), paint);
        cropImageView.setImageBitmap(canvasBitmap);
        destBitmap = canvasBitmap;
    }

    private void initView() {
        QMUITopBar topBar = findViewById(R.id.top_bar);
        topBar.setTitle("调整背景颜色");
        topBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        findViewById(R.id.btn_next).setOnClickListener(this);
        findViewById(R.id.ll_white).setOnClickListener(this);
        findViewById(R.id.ll_red).setOnClickListener(this);
        findViewById(R.id.ll_blue).setOnClickListener(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        disposables.dispose();
        if (destBitmap != null && !destBitmap.isRecycled()) {
            destBitmap.recycle();
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_white:
                changePos(0);
                break;
            case R.id.ll_blue:
                changePos(1);
                break;
            case R.id.ll_red:
                changePos(2);
                break;
            case R.id.btn_next:
                getNext();
                break;
        }

    }

    private void getNext() {
        drawImage(curPos, true);
        String filePath = ImageUtils.save2Album(destBitmap == null ? BitmapFactory.decodeFile(resultPath) : destBitmap, Bitmap.CompressFormat.JPEG).getPath();
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");
        Intent intent = new Intent(PicturePreviewActivity.this, SavePicActivity.class);
        intent.putExtra(PICK_IMG, filePath);
        intent.putExtra("SIZE", picSize);
        startActivity(intent);
    }

    private void changePos(int pos) {
        for (int i = 0; i < ids.length; i++) {
            findViewById(ids[i]).setVisibility(pos == i ? View.VISIBLE : View.INVISIBLE);
        }
        drawImage(pos, false);
    }

}
