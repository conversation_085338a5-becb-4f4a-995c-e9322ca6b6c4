# 新API接口集成文档

## 概述

已成功集成新的人脸检测接口和扩展的六寸排版照生成接口，支持多种人脸检测模型和相纸尺寸选择。

## 新增功能

### 1. 新的人脸检测接口 `/face_detection`

#### 支持的检测模型
- **MTCNN**: 高精度人脸检测，支持关键点检测
- **RetinaFace-ResNet50**: 基于ResNet50的高性能检测
- **Face++**: 在线API，功能最全面

#### 新增参数
- `face_detect_model`: 选择检测模型
- `return_landmarks`: 是否返回关键点信息
- `return_attributes`: 是否返回人脸属性

#### 响应数据增强
- 人脸置信度
- 人脸旋转角度
- 人脸中心点坐标
- 关键点信息（可选）
- 图片基本信息

### 2. 扩展的六寸排版照接口 `/generate_layout_photos`

#### 新增相纸尺寸支持
- **6 inch**: 六寸 (1205x1795)
- **5 inch**: 五寸 (1051x1500)
- **A4**: A4纸 (2479x3508)
- **3R**: 3R规格 (1051x1500)
- **4R**: 4R规格 (1205x1795)

#### 新增功能
- `paper_size`: 相纸尺寸选择
- `crop_line`: 裁剪线开关

## 技术实现

### 1. 数据模型

#### NewFaceDetectData.java
```java
public class NewFaceDetectData {
    public boolean status;
    public int face_count;
    public List<Face> faces;
    public ImageInfo image_info;
    public String model_used;
    public String error;
    
    public static class Face {
        public Rectangle rectangle;
        public Center center;
        public float roll_angle;
        public float confidence;
        public int area;
        public Landmarks landmarks;
    }
}
```

#### 兼容性转换
- 提供 `toFaceDetectData()` 方法转换为旧格式
- 保持向后兼容性

### 2. API配置扩展

#### ApiConfig.java 新增配置
```java
// 新的人脸检测API
public static final String NEW_FACE_DETECT_URL = "https://api.juyingnj.com/face_detection";
public static final String NEW_FACE_DETECT_TOKEN = "aabbcc";

// 人脸检测模型
public static final String FACE_MODEL_MTCNN = "mtcnn";
public static final String FACE_MODEL_RETINAFACE = "retinaface-resnet50";
public static final String FACE_MODEL_FACEPP = "face++";

// 相纸尺寸
public static final String PAPER_SIZE_6_INCH = "6 inch";
public static final String PAPER_SIZE_5_INCH = "5 inch";
public static final String PAPER_SIZE_A4 = "A4";
```

### 3. 服务层增强

#### FaceDetectionService.java
```java
// 新API检测方法
public void detectFaceWithNewApi(String imagePath, String faceModel, ApiCallback<FaceDetectData> callback)

// 完整参数检测
public void detectFaceWithNewApi(String imagePath, String faceModel, 
                               boolean returnLandmarks, boolean returnAttributes, 
                               ApiCallback<FaceDetectData> callback)
```

#### LayoutPhotoService.java
```java
// 相纸尺寸选择
public void generateLayoutPhotoWithPaperSize(String imagePath, PicSize picSize, String paperSize, ApiCallback<Bitmap> callback)

// 6寸带裁剪线
public void generate6InchLayoutPhoto(String imagePath, PicSize picSize, boolean withCropLine, ApiCallback<Bitmap> callback)

// A4排版
public void generateA4LayoutPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback)
```

## 使用示例

### 1. 新人脸检测API

#### 基础使用
```java
FaceDetectionService.getInstance().detectFaceWithNewApi(
    imagePath, 
    ApiConfig.FACE_MODEL_MTCNN, 
    new ApiCallback<FaceDetectData>() {
        @Override
        public void onSuccess(FaceDetectData faceData) {
            // 处理检测结果
        }
        
        @Override
        public void onError(String error) {
            // 处理错误
        }
    }
);
```

#### 完整参数使用
```java
FaceDetectionService.getInstance().detectFaceWithNewApi(
    imagePath,
    ApiConfig.FACE_MODEL_RETINAFACE,
    true,  // 返回关键点
    false, // 不返回属性
    callback
);
```

### 2. 扩展排版照API

#### 选择相纸尺寸
```java
LayoutPhotoService.getInstance().generateLayoutPhotoWithPaperSize(
    imagePath,
    picSize,
    ApiConfig.PAPER_SIZE_A4,
    callback
);
```

#### 6寸带裁剪线
```java
LayoutPhotoService.getInstance().generate6InchLayoutPhoto(
    imagePath,
    picSize,
    true, // 包含裁剪线
    callback
);
```

#### 通过IdPhotoManager使用
```java
IdPhotoManager.getInstance().generateA4LayoutPhoto(imagePath, picSize, callback);
IdPhotoManager.getInstance().generate6InchLayoutPhoto(imagePath, picSize, true, callback);
```

## 配置选项

### 1. 人脸检测模型选择

#### MTCNN (推荐)
- 高精度检测
- 支持关键点
- 适合证件照应用

#### RetinaFace-ResNet50
- 高性能检测
- 速度较快
- 适合批量处理

#### Face++
- 在线API
- 功能最全面
- 需要网络连接

### 2. 相纸尺寸选择

#### 常用尺寸
- **6寸**: 最常用的证件照排版尺寸
- **A4**: 适合打印和存档
- **5寸**: 紧凑型排版

#### 自定义配置
```java
LayoutConfig config = new LayoutConfig()
    .setPaperSize(ApiConfig.PAPER_SIZE_A4)
    .setCropLine(true)
    .setDpi("300")
    .setKbLimit("200");
```

## 性能优化

### 1. 模型选择策略
- 证件照应用优先使用 MTCNN
- 批量处理使用 RetinaFace
- 需要高级功能时使用 Face++

### 2. 相纸尺寸优化
- 预览时使用较小尺寸
- 最终输出使用目标尺寸
- 根据用途选择合适的DPI

### 3. 缓存策略
- 检测结果可以缓存
- 相同参数的排版结果可复用
- 减少重复API调用

## 错误处理

### 1. 人脸检测错误
```java
if (!newData.status) {
    callback.onError("人脸检测失败: " + newData.error);
}
```

### 2. 模型不支持
- 自动降级到默认模型
- 提供用户友好的错误信息

### 3. 网络异常
- 实现重试机制
- 提供离线备用方案

## 向后兼容性

### 1. 数据格式兼容
- 新数据模型提供转换方法
- 旧代码无需修改

### 2. API接口兼容
- 保留原有接口
- 新接口作为扩展

### 3. 配置兼容
- 默认值保持不变
- 新参数为可选

## 测试建议

### 1. 功能测试
- 测试所有检测模型
- 验证所有相纸尺寸
- 检查裁剪线功能

### 2. 性能测试
- 对比不同模型的速度
- 测试大批量处理
- 监控内存使用

### 3. 兼容性测试
- 验证旧代码正常工作
- 测试数据格式转换
- 检查错误处理

## 未来扩展

### 1. 更多检测模型
- 支持更多AI模型
- 本地模型集成
- 自定义模型支持

### 2. 更多相纸规格
- 国际标准尺寸
- 自定义尺寸支持
- 动态尺寸计算

### 3. 高级功能
- 批量处理接口
- 异步处理支持
- 进度回调机制
