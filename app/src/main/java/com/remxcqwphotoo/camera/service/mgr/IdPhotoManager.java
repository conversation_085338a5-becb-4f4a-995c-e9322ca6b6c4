package com.remxcqwphotoo.camera.service;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;

import com.remxcqwphotoo.camera.model.FaceDetectData;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import java.io.File;

import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

/**
 * 证件照服务管理器
 * 提供统一的证件照处理入口，整合所有相关服务
 */
public class IdPhotoManager {

    private static IdPhotoManager instance;

    private final IdPhotoService idPhotoService;
    private final LayoutPhotoService layoutPhotoService;
    private final FallbackService fallbackService;

    private IdPhotoManager() {
        this.idPhotoService = IdPhotoService.getInstance();
        this.layoutPhotoService = LayoutPhotoService.getInstance();
        this.fallbackService = FallbackService.getInstance();
    }

    public static IdPhotoManager getInstance() {
        if (instance == null) {
            synchronized (IdPhotoManager.class) {
                if (instance == null) {
                    instance = new IdPhotoManager();
                }
            }
        }
        return instance;
    }


    // ========== ID照片生成相关 ==========

    /**
     * 生成标准证件照
     */
    public void generateIdPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        idPhotoService.generateIdPhoto(imagePath, picSize, callback);
    }


    /**
     * 生成证件照（带完整fallback策略）
     */
    public void generateIdPhotoWithFallback(Context context, String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        // 第一层：尝试新的ID照片服务
        generateIdPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (callback != null) callback.onStart();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                if (callback != null) {
                    callback.onSuccess(bitmap);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                // 第二层：使用百度API备用方案
                fallbackService.baiduBodySegmentation(context, imagePath, new ApiCallback<Bitmap>() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        if (callback != null) {
                            callback.onSuccess(bitmap);
                            callback.onComplete();
                        }
                    }

                    @Override
                    public void onError(String fallbackError) {
                        // 第三层：本地处理作为最终备用
                        fallbackService.localImageProcessing(imagePath, callback);
                    }
                });
            }
        });
    }

    // ========== 6寸排版照片相关 ==========

    /**
     * 生成6寸排版照片
     */
    public void generateLayoutPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        layoutPhotoService.generateLayoutPhoto(imagePath, picSize, callback);
    }


    /**
     * 生成排版照片（带完整fallback策略）
     */
    public void generateLayoutPhotoWithFallback(Context context, String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        // 第一层：尝试新的排版服务
        generateLayoutPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (callback != null) callback.onStart();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                if (callback != null) {
                    callback.onSuccess(bitmap);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                // 第二层：使用本地排版处理作为备用
                generateLocalLayoutPhoto(context, imagePath, picSize, callback);
            }
        });
    }

    /**
     * 本地排版照片生成（备用方案）
     */
    private void generateLocalLayoutPhoto(Context context, String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        // 这里可以实现本地的排版逻辑，或者直接返回单张照片
        fallbackService.localImageProcessing(imagePath, new ApiCallback<Bitmap>() {
            @Override
            public void onSuccess(Bitmap bitmap) {
                // 可以在这里添加简单的本地排版逻辑
                // 目前直接返回单张照片作为备用
                if (callback != null) {
                    callback.onSuccess(bitmap);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError("所有排版方案都失败了: " + error);
                    callback.onComplete();
                }
            }
        });
    }

}
