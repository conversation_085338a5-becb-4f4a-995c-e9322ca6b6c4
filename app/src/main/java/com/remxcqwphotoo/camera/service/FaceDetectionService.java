package com.remxcqwphotoo.camera.service;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;

import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.model.NewFaceDetectData;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import java.io.File;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;
import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

/**
 * 人脸检测服务
 * 负责处理人脸检测相关的API调用
 */
public class FaceDetectionService {

    private static FaceDetectionService instance;

    private FaceDetectionService() {
    }

    public static FaceDetectionService getInstance() {
        if (instance == null) {
            synchronized (FaceDetectionService.class) {
                if (instance == null) {
                    instance = new FaceDetectionService();
                }
            }
        }
        return instance;
    }

    /**
     * 检测人脸
     *
     * @param context   上下文
     * @param imagePath 图片路径
     * @param callback  回调接口
     */
    public void detectFace(Context context, String imagePath, ApiCallback<NewFaceDetectData> callback) {
        if (callback != null) {
            callback.onStart();
        }

        // 先压缩图片
        compressImage(context, imagePath, new ApiCallback<String>() {
            @Override
            public void onSuccess(String compressedPath) {
                performFaceDetection(compressedPath, callback);
            }

            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError("图片压缩失败: " + error);
                }
            }
        });
    }

    /**
     * 压缩图片
     */
    private void compressImage(Context context, String imagePath, ApiCallback<String> callback) {
        Log.d("FaceDetectionService", "开始压缩图片: " + imagePath);

        // 获取原始图片信息
        File originalFile = new File(imagePath);
        if (!originalFile.exists()) {
            Log.e("FaceDetectionService", "原始图片文件不存在: " + imagePath);
            if (callback != null) {
                callback.onError("图片文件不存在");
            }
            return;
        }

        long originalSize = originalFile.length();
        Log.d("FaceDetectionService", "原始图片大小: " + formatFileSize(originalSize));

        // 获取原始图片尺寸
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(imagePath, options);
        int originalWidth = options.outWidth;
        int originalHeight = options.outHeight;
        Log.d("FaceDetectionService", "原始图片尺寸: " + originalWidth + "x" + originalHeight);

        Luban.with(context)
                .load(imagePath)
                .ignoreBy(100) // 小于100KB的图片不压缩
                .setCompressListener(new OnCompressListener() {
                    @Override
                    public void onStart() {
                        Log.d("FaceDetectionService", "Luban压缩开始");
                    }

                    @Override
                    public void onSuccess(File file) {
                        long compressedSize = file.length();
                        Log.d("FaceDetectionService", "压缩完成: " + file.getAbsolutePath());
                        Log.d("FaceDetectionService", "压缩后大小: " + formatFileSize(compressedSize));
                        Log.d("FaceDetectionService", "压缩比例: " + String.format("%.2f%%", (double) compressedSize / originalSize * 100));

                        // 获取压缩后图片尺寸
                        BitmapFactory.Options compressedOptions = new BitmapFactory.Options();
                        compressedOptions.inJustDecodeBounds = true;
                        BitmapFactory.decodeFile(file.getAbsolutePath(), compressedOptions);
                        int compressedWidth = compressedOptions.outWidth;
                        int compressedHeight = compressedOptions.outHeight;
                        Log.d("FaceDetectionService", "压缩后尺寸: " + compressedWidth + "x" + compressedHeight);

                        if (callback != null) {
                            callback.onSuccess(file.getAbsolutePath());
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        Log.e("FaceDetectionService", "图片压缩失败", e);
                        if (callback != null) {
                            callback.onError(e.getMessage());
                        }
                    }
                }).launch();
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        }
    }

    /**
     * 直接执行人脸检测（跳过压缩，用于已压缩的图片）
     */
    public void performDirectFaceDetection(String imagePath, ApiCallback<NewFaceDetectData> callback) {
        performFaceDetection(imagePath, callback);
    }

    /**
     * 使用新的人脸检测API
     *
     * @param imagePath 图片路径
     * @param faceModel 人脸检测模型
     * @param callback  回调接口
     */
    public void detectFaceWithNewApi(String imagePath, String faceModel, ApiCallback<NewFaceDetectData> callback) {
        detectFaceWithNewApi(imagePath, faceModel, true, false, callback);
    }

    /**
     * 使用新的人脸检测API（完整参数）
     *
     * @param imagePath        图片路径
     * @param faceModel        人脸检测模型
     * @param returnLandmarks  是否返回关键点
     * @param returnAttributes 是否返回属性
     * @param callback         回调接口
     */
    @SuppressLint("CheckResult")
    public void detectFaceWithNewApi(String imagePath, String faceModel, boolean returnLandmarks,
                                     boolean returnAttributes, ApiCallback<NewFaceDetectData> callback) {
        if (callback != null) {
            callback.onStart();
        }

        File imageFile = new File(imagePath);

        RxHttp.postForm(ApiConfig.NEW_FACE_DETECT_URL)
                .addHeader("token", ApiConfig.NEW_FACE_DETECT_TOKEN)
                .addFile("input_image", imageFile)
                .add("face_detect_model", faceModel)
                .add("return_landmarks", true)
                .add("return_attributes", true)
                .asClass(NewFaceDetectData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<NewFaceDetectData>() {
                    @Override
                    public void accept(NewFaceDetectData newData) throws Exception {
                        if (newData.status) {
                            // 设置检测使用的图片路径
                            newData.image_path = imagePath;
                            if (callback != null) {
                                callback.onSuccess(newData);
                                callback.onComplete();
                            }
                        } else {
                            if (callback != null) {
                                callback.onError("人脸检测失败: " + newData.error);
                                callback.onComplete();
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("人脸检测网络请求失败: " + throwable.getMessage());
                            callback.onComplete();
                        }
                    }
                });
    }

    /**
     * 执行人脸检测API调用
     */
    @SuppressLint("CheckResult")
    private void performFaceDetection(String imagePath, ApiCallback<NewFaceDetectData> callback) {
        // 直接使用新API，不再使用旧API
        detectFaceWithNewApi(imagePath, ApiConfig.DEFAULT_FACE_MODEL, true, false, callback);
    }


}
