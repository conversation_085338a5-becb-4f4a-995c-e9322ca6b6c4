package com.remxcqwphotoo.camera.service;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.model.FaceDetectData;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import java.io.File;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;
import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

/**
 * 人脸检测服务
 * 负责处理人脸检测相关的API调用
 */
public class FaceDetectionService {
    
    private static FaceDetectionService instance;
    
    private FaceDetectionService() {}
    
    public static FaceDetectionService getInstance() {
        if (instance == null) {
            synchronized (FaceDetectionService.class) {
                if (instance == null) {
                    instance = new FaceDetectionService();
                }
            }
        }
        return instance;
    }
    
    /**
     * 检测人脸
     * @param context 上下文
     * @param imagePath 图片路径
     * @param callback 回调接口
     */
    public void detectFace(Context context, String imagePath, ApiCallback<FaceDetectData> callback) {
        if (callback != null) {
            callback.onStart();
        }
        
        // 先压缩图片
        compressImage(context, imagePath, new ApiCallback<String>() {
            @Override
            public void onSuccess(String compressedPath) {
                performFaceDetection(compressedPath, callback);
            }
            
            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError("图片压缩失败: " + error);
                }
            }
        });
    }
    
    /**
     * 压缩图片
     */
    private void compressImage(Context context, String imagePath, ApiCallback<String> callback) {
        Luban.with(context)
                .load(imagePath)
                .ignoreBy(100)
                .setCompressListener(new OnCompressListener() {
                    @Override
                    public void onStart() {
                        // 压缩开始
                    }
                    
                    @Override
                    public void onSuccess(File file) {
                        if (callback != null) {
                            callback.onSuccess(file.getAbsolutePath());
                        }
                    }
                    
                    @Override
                    public void onError(Throwable e) {
                        if (callback != null) {
                            callback.onError(e.getMessage());
                        }
                    }
                }).launch();
    }
    
    /**
     * 执行人脸检测API调用
     */
    @SuppressLint("CheckResult")
    private void performFaceDetection(String imagePath, ApiCallback<FaceDetectData> callback) {
        File imageFile = new File(imagePath);
        
        RxHttp.postForm(ApiConfig.FACE_DETECT_URL)
                .addHeader("Authorization", "Bearer " + ApiConfig.FACE_DETECT_TOKEN)
                .addHeader("accept", "application/json")
                .addFile("file", imageFile)
                .asClass(FaceDetectData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<FaceDetectData>() {
                    @Override
                    public void accept(FaceDetectData data) throws Exception {
                        if (callback != null) {
                            callback.onSuccess(data);
                            callback.onComplete();
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("人脸检测失败: " + throwable.getMessage());
                            callback.onComplete();
                        }
                    }
                });
    }
    
    /**
     * 验证人脸质量
     */
    public boolean validateFaceQuality(FaceDetectData.FaceLocation faceLocation) {
        if (faceLocation == null) {
            return false;
        }
        
        // 检查人脸区域大小
        int width = faceLocation.getWidth();
        int height = faceLocation.getHeight();
        
        if (width < 100 || height < 100) {
            return false;
        }
        
        // 检查宽高比
        float ratio = (float) width / height;
        if (ratio < 0.5f || ratio > 1.2f) {
            return false;
        }
        
        // 检查边缘位置
        if (faceLocation.left < 50 || faceLocation.top < 50) {
            return false;
        }
        
        return true;
    }
}
