package com.remxcqwphotoo.camera.service.callback;

/**
 * 通用API回调接口
 */
public interface ApiCallback<T> {
    
    /**
     * API调用成功
     * @param result 返回结果
     */
    void onSuccess(T result);
    
    /**
     * API调用失败
     * @param error 错误信息
     */
    void onError(String error);
    
    /**
     * API调用开始（可选实现）
     */
    default void onStart() {
        // 默认空实现
    }
    
    /**
     * API调用完成（可选实现）
     */
    default void onComplete() {
        // 默认空实现
    }
}
