package com.remxcqwphotoo.camera.service;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.text.TextUtils;

import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.model.SixInchLayoutData;
import com.remxcqwphotoo.camera.net.Base64Utils;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import java.io.File;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;

/**
 * 6寸排版照片服务
 * 负责处理6寸排版照片生成相关的API调用
 */
public class LayoutPhotoService {
    
    private static LayoutPhotoService instance;
    
    private LayoutPhotoService() {}
    
    public static LayoutPhotoService getInstance() {
        if (instance == null) {
            synchronized (LayoutPhotoService.class) {
                if (instance == null) {
                    instance = new LayoutPhotoService();
                }
            }
        }
        return instance;
    }
    
    /**
     * 生成6寸排版照片
     * @param imagePath 原始图片路径
     * @param picSize 照片尺寸
     * @param callback 回调接口
     */
    public void generateLayoutPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        generateLayoutPhoto(imagePath, picSize, new LayoutConfig(), callback);
    }
    
    /**
     * 生成6寸排版照片（带自定义配置）
     * @param imagePath 原始图片路径
     * @param picSize 照片尺寸
     * @param config 排版配置
     * @param callback 回调接口
     */
    @SuppressLint("CheckResult")
    public void generateLayoutPhoto(String imagePath, PicSize picSize, LayoutConfig config, ApiCallback<Bitmap> callback) {
        if (callback != null) {
            callback.onStart();
        }
        
        File imageFile = new File(imagePath);
        
        RxHttp.postForm(ApiConfig.LAYOUT_PHOTO_URL)
                .addHeader("token", ApiConfig.LAYOUT_PHOTO_TOKEN)
                .addFile("input_image", imageFile)
                .add("height", String.valueOf(picSize.height))
                .add("width", String.valueOf(picSize.width))
                .add("kb", config.kbLimit)
                .add("dpi", config.dpi)
                .asClass(SixInchLayoutData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<SixInchLayoutData>() {
                    @Override
                    public void accept(SixInchLayoutData layoutData) throws Exception {
                        try {
                            if (layoutData.status && !TextUtils.isEmpty(layoutData.image_base64)) {
                                // 解析base64图片
                                String base64Data = layoutData.image_base64;
                                if (base64Data.startsWith("data:image/")) {
                                    base64Data = base64Data.substring(base64Data.indexOf(",") + 1);
                                }
                                Bitmap bitmap = Base64Utils.base64ToImage(base64Data);
                                
                                if (callback != null) {
                                    callback.onSuccess(bitmap);
                                    callback.onComplete();
                                }
                            } else {
                                if (callback != null) {
                                    callback.onError("排版照片生成失败");
                                    callback.onComplete();
                                }
                            }
                        } catch (Exception e) {
                            if (callback != null) {
                                callback.onError("排版照片处理失败: " + e.getMessage());
                                callback.onComplete();
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + throwable.getMessage());
                            callback.onComplete();
                        }
                    }
                });
    }
    
    /**
     * 生成预览图（低质量，用于快速预览）
     * @param imagePath 原始图片路径
     * @param picSize 照片尺寸
     * @param callback 回调接口
     */
    public void generatePreview(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        LayoutConfig previewConfig = new LayoutConfig()
                .setKbLimit("100") // 预览用较小的文件大小
                .setDpi("150"); // 预览用较低的DPI
        
        generateLayoutPhoto(imagePath, picSize, previewConfig, callback);
    }
    
    /**
     * 排版配置类
     */
    public static class LayoutConfig {
        public String kbLimit = ApiConfig.DEFAULT_KB_LIMIT;
        public String dpi = ApiConfig.DEFAULT_DPI;
        
        public LayoutConfig setKbLimit(String kbLimit) {
            this.kbLimit = kbLimit;
            return this;
        }
        
        public LayoutConfig setDpi(String dpi) {
            this.dpi = dpi;
            return this;
        }
    }
}
