package com.remxcqwphotoo.camera.service;

import android.content.Context;
import android.graphics.Bitmap;

import com.remxcqwphotoo.camera.model.FaceDetectData;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

/**
 * 证件照服务管理器
 * 提供统一的证件照处理入口，整合所有相关服务
 */
public class IdPhotoManager {
    
    private static IdPhotoManager instance;
    
    private final FaceDetectionService faceDetectionService;
    private final IdPhotoService idPhotoService;
    private final LayoutPhotoService layoutPhotoService;
    private final FallbackService fallbackService;
    
    private IdPhotoManager() {
        this.faceDetectionService = FaceDetectionService.getInstance();
        this.idPhotoService = IdPhotoService.getInstance();
        this.layoutPhotoService = LayoutPhotoService.getInstance();
    }
    
    public static IdPhotoManager getInstance() {
        if (instance == null) {
            synchronized (IdPhotoManager.class) {
                if (instance == null) {
                    instance = new IdPhotoManager();
                }
            }
        }
        return instance;
    }
    
    // ========== 人脸检测相关 ==========
    
    /**
     * 检测人脸
     */
    public void detectFace(Context context, String imagePath, ApiCallback<FaceDetectData> callback) {
        faceDetectionService.detectFace(context, imagePath, callback);
    }
    
    /**
     * 验证人脸质量
     */
    public boolean validateFaceQuality(FaceDetectData.FaceLocation faceLocation) {
        return faceDetectionService.validateFaceQuality(faceLocation);
    }
    
    // ========== ID照片生成相关 ==========
    
    /**
     * 生成标准证件照
     */
    public void generateIdPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        idPhotoService.generateIdPhoto(imagePath, picSize, callback);
    }
    
    /**
     * 生成自定义配置的证件照
     */
    public void generateIdPhoto(String imagePath, PicSize picSize, IdPhotoService.IdPhotoConfig config, ApiCallback<Bitmap> callback) {
        idPhotoService.generateIdPhoto(imagePath, picSize, config, callback);
    }
    
    // ========== 6寸排版照片相关 ==========
    
    /**
     * 生成6寸排版照片
     */
    public void generateLayoutPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        layoutPhotoService.generateLayoutPhoto(imagePath, picSize, callback);
    }
    
    /**
     * 生成6寸排版照片预览
     */
    public void generateLayoutPreview(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        layoutPhotoService.generatePreview(imagePath, picSize, callback);
    }
    
    /**
     * 生成自定义配置的6寸排版照片
     */
    public void generateLayoutPhoto(String imagePath, PicSize picSize, LayoutPhotoService.LayoutConfig config, ApiCallback<Bitmap> callback) {
        layoutPhotoService.generateLayoutPhoto(imagePath, picSize, config, callback);
    }
    
    // ========== 完整工作流程 ==========
    
    /**
     * 完整的证件照处理流程：检测 -> 生成 -> 排版
     * @param context 上下文
     * @param imagePath 原始图片路径
     * @param picSize 照片尺寸
     * @param callback 最终回调
     */
    public void processIdPhotoComplete(Context context, String imagePath, PicSize picSize, ApiCallback<ProcessResult> callback) {
        if (callback != null) {
            callback.onStart();
        }
        
        // 第一步：人脸检测
        detectFace(context, imagePath, new ApiCallback<FaceDetectData>() {
            @Override
            public void onSuccess(FaceDetectData faceData) {
                if (faceData.face_count == 0) {
                    if (callback != null) {
                        callback.onError("未检测到人脸");
                    }
                    return;
                }
                
                if (faceData.face_count > 1) {
                    if (callback != null) {
                        callback.onError("检测到多张人脸，请使用单人照片");
                    }
                    return;
                }
                
                // 验证人脸质量
                FaceDetectData.FaceLocation faceLocation = faceData.face_locations.get(0);
                if (!validateFaceQuality(faceLocation)) {
                    if (callback != null) {
                        callback.onError("人脸质量不符合要求");
                    }
                    return;
                }
                
                // 第二步：生成证件照
                generateIdPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
                    @Override
                    public void onSuccess(Bitmap idPhoto) {
                        // 第三步：生成排版照片
                        generateLayoutPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
                            @Override
                            public void onSuccess(Bitmap layoutPhoto) {
                                if (callback != null) {
                                    ProcessResult result = new ProcessResult(faceData, idPhoto, layoutPhoto);
                                    callback.onSuccess(result);
                                    callback.onComplete();
                                }
                            }
                            
                            @Override
                            public void onError(String error) {
                                if (callback != null) {
                                    callback.onError("排版照片生成失败: " + error);
                                    callback.onComplete();
                                }
                            }
                        });
                    }
                    
                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError("证件照生成失败: " + error);
                            callback.onComplete();
                        }
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError("人脸检测失败: " + error);
                    callback.onComplete();
                }
            }
        });
    }
    
    /**
     * 处理结果类
     */
    public static class ProcessResult {
        public final FaceDetectData faceData;
        public final Bitmap idPhoto;
        public final Bitmap layoutPhoto;
        
        public ProcessResult(FaceDetectData faceData, Bitmap idPhoto, Bitmap layoutPhoto) {
            this.faceData = faceData;
            this.idPhoto = idPhoto;
            this.layoutPhoto = layoutPhoto;
        }
    }
}
