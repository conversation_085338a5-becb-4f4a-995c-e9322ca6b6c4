package com.remxcqwphotoo.camera.service;

import android.content.Context;
import android.graphics.Bitmap;

import com.remxcqwphotoo.camera.model.FaceDetectData;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

/**
 * 证件照服务管理器
 * 提供统一的证件照处理入口，整合所有相关服务
 */
public class IdPhotoManager {
    
    private static IdPhotoManager instance;
    
    private final FaceDetectionService faceDetectionService;
    private final IdPhotoService idPhotoService;
    private final LayoutPhotoService layoutPhotoService;
    private final FallbackService fallbackService;
    
    private IdPhotoManager() {
        this.faceDetectionService = FaceDetectionService.getInstance();
        this.idPhotoService = IdPhotoService.getInstance();
        this.layoutPhotoService = LayoutPhotoService.getInstance();
        this.fallbackService = FallbackService.getInstance();
    }
    
    public static IdPhotoManager getInstance() {
        if (instance == null) {
            synchronized (IdPhotoManager.class) {
                if (instance == null) {
                    instance = new IdPhotoManager();
                }
            }
        }
        return instance;
    }
    
    // ========== 人脸检测相关 ==========
    
    /**
     * 检测人脸
     */
    public void detectFace(Context context, String imagePath, ApiCallback<FaceDetectData> callback) {
        faceDetectionService.detectFace(context, imagePath, callback);
    }
    
    /**
     * 检测人脸（带完整fallback策略）
     */
    public void detectFaceWithFallback(Context context, String imagePath, ApiCallback<FaceDetectData> callback) {
        // 第一层：尝试新的人脸检测服务
        detectFace(context, imagePath, new ApiCallback<FaceDetectData>() {
            @Override
            public void onStart() {
                if (callback != null) callback.onStart();
            }

            @Override
            public void onSuccess(FaceDetectData faceData) {
                if (callback != null) {
                    callback.onSuccess(faceData);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                // 第二层：使用百度API备用方案
                fallbackService.baiduFaceDetection(context, imagePath, new ApiCallback<FaceDetectData>() {
                    @Override
                    public void onSuccess(FaceDetectData faceData) {
                        if (callback != null) {
                            callback.onSuccess(faceData);
                            callback.onComplete();
                        }
                    }

                    @Override
                    public void onError(String fallbackError) {
                        // 所有检测方案都失败了
                        if (callback != null) {
                            callback.onError("人脸检测失败，请检查网络连接或重试");
                            callback.onComplete();
                        }
                    }
                });
            }
        });
    }

    /**
     * 验证人脸质量
     */
    public boolean validateFaceQuality(FaceDetectData.FaceLocation faceLocation) {
        return faceDetectionService.validateFaceQuality(faceLocation);
    }
    
    // ========== ID照片生成相关 ==========
    
    /**
     * 生成标准证件照
     */
    public void generateIdPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        idPhotoService.generateIdPhoto(imagePath, picSize, callback);
    }
    
    /**
     * 生成自定义配置的证件照
     */
    public void generateIdPhoto(String imagePath, PicSize picSize, IdPhotoService.IdPhotoConfig config, ApiCallback<Bitmap> callback) {
        idPhotoService.generateIdPhoto(imagePath, picSize, config, callback);
    }

    /**
     * 生成证件照（带完整fallback策略）
     */
    public void generateIdPhotoWithFallback(Context context, String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        // 第一层：尝试新的ID照片服务
        generateIdPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (callback != null) callback.onStart();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                if (callback != null) {
                    callback.onSuccess(bitmap);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                // 第二层：使用百度API备用方案
                fallbackService.baiduBodySegmentation(context, imagePath, new ApiCallback<Bitmap>() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        if (callback != null) {
                            callback.onSuccess(bitmap);
                            callback.onComplete();
                        }
                    }

                    @Override
                    public void onError(String fallbackError) {
                        // 第三层：本地处理作为最终备用
                        fallbackService.localImageProcessing(imagePath, callback);
                    }
                });
            }
        });
    }
    
    // ========== 6寸排版照片相关 ==========
    
    /**
     * 生成6寸排版照片
     */
    public void generateLayoutPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        layoutPhotoService.generateLayoutPhoto(imagePath, picSize, callback);
    }
    
    /**
     * 生成6寸排版照片预览
     */
    public void generateLayoutPreview(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        layoutPhotoService.generatePreview(imagePath, picSize, callback);
    }
    
    /**
     * 生成自定义配置的6寸排版照片
     */
    public void generateLayoutPhoto(String imagePath, PicSize picSize, LayoutPhotoService.LayoutConfig config, ApiCallback<Bitmap> callback) {
        layoutPhotoService.generateLayoutPhoto(imagePath, picSize, config, callback);
    }

    /**
     * 生成排版照片（带完整fallback策略）
     */
    public void generateLayoutPhotoWithFallback(Context context, String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        // 第一层：尝试新的排版服务
        generateLayoutPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (callback != null) callback.onStart();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                if (callback != null) {
                    callback.onSuccess(bitmap);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                // 第二层：使用本地排版处理作为备用
                generateLocalLayoutPhoto(context, imagePath, picSize, callback);
            }
        });
    }

    /**
     * 本地排版照片生成（备用方案）
     */
    private void generateLocalLayoutPhoto(Context context, String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        // 这里可以实现本地的排版逻辑，或者直接返回单张照片
        fallbackService.localImageProcessing(imagePath, new ApiCallback<Bitmap>() {
            @Override
            public void onSuccess(Bitmap bitmap) {
                // 可以在这里添加简单的本地排版逻辑
                // 目前直接返回单张照片作为备用
                if (callback != null) {
                    callback.onSuccess(bitmap);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError("所有排版方案都失败了: " + error);
                    callback.onComplete();
                }
            }
        });
    }
    
    // ========== 完整工作流程 ==========
    
    /**
     * 完整的证件照处理流程：检测 -> 生成 -> 排版
     * @param context 上下文
     * @param imagePath 原始图片路径
     * @param picSize 照片尺寸
     * @param callback 最终回调
     */
    public void processIdPhotoComplete(Context context, String imagePath, PicSize picSize, ApiCallback<ProcessResult> callback) {
        if (callback != null) {
            callback.onStart();
        }
        
        // 第一步：人脸检测
        detectFace(context, imagePath, new ApiCallback<FaceDetectData>() {
            @Override
            public void onSuccess(FaceDetectData faceData) {
                if (faceData.face_count == 0) {
                    if (callback != null) {
                        callback.onError("未检测到人脸");
                    }
                    return;
                }
                
                if (faceData.face_count > 1) {
                    if (callback != null) {
                        callback.onError("检测到多张人脸，请使用单人照片");
                    }
                    return;
                }
                
                // 验证人脸质量
                FaceDetectData.FaceLocation faceLocation = faceData.face_locations.get(0);
                if (!validateFaceQuality(faceLocation)) {
                    if (callback != null) {
                        callback.onError("人脸质量不符合要求");
                    }
                    return;
                }
                
                // 第二步：生成证件照
                generateIdPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
                    @Override
                    public void onSuccess(Bitmap idPhoto) {
                        // 第三步：生成排版照片
                        generateLayoutPhoto(imagePath, picSize, new ApiCallback<Bitmap>() {
                            @Override
                            public void onSuccess(Bitmap layoutPhoto) {
                                if (callback != null) {
                                    ProcessResult result = new ProcessResult(faceData, idPhoto, layoutPhoto);
                                    callback.onSuccess(result);
                                    callback.onComplete();
                                }
                            }
                            
                            @Override
                            public void onError(String error) {
                                if (callback != null) {
                                    callback.onError("排版照片生成失败: " + error);
                                    callback.onComplete();
                                }
                            }
                        });
                    }
                    
                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError("证件照生成失败: " + error);
                            callback.onComplete();
                        }
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError("人脸检测失败: " + error);
                    callback.onComplete();
                }
            }
        });
    }
    
    /**
     * 处理结果类
     */
    public static class ProcessResult {
        public final FaceDetectData faceData;
        public final Bitmap idPhoto;
        public final Bitmap layoutPhoto;
        
        public ProcessResult(FaceDetectData faceData, Bitmap idPhoto, Bitmap layoutPhoto) {
            this.faceData = faceData;
            this.idPhoto = idPhoto;
            this.layoutPhoto = layoutPhoto;
        }
    }
}
