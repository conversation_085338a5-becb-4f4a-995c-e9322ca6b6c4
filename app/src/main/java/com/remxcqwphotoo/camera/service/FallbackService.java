package com.remxcqwphotoo.camera.service;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.util.Log;

import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.model.BodyData;
import com.remxcqwphotoo.camera.model.FaceDetectData;
import com.remxcqwphotoo.camera.model.SegData;
import com.remxcqwphotoo.camera.model.TokenData;
import com.remxcqwphotoo.camera.net.Base64Utils;
import com.remxcqwphotoo.camera.net.Url;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import java.io.File;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;
import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

/**
 * Fallback服务
 * 提供各种API的备用方案，确保功能的可用性
 */
public class FallbackService {

    private static FallbackService instance;
    private final LocalImageProcessingService localProcessingService;
    
    private FallbackService() {
        this.localProcessingService = LocalImageProcessingService.getInstance();
    }
    
    public static FallbackService getInstance() {
        if (instance == null) {
            synchronized (FallbackService.class) {
                if (instance == null) {
                    instance = new FallbackService();
                }
            }
        }
        return instance;
    }
    
    /**
     * 百度API人体分割备用方案
     * @param context 上下文
     * @param imagePath 图片路径
     * @param callback 回调
     */
    public void baiduBodySegmentation(Context context, String imagePath, ApiCallback<Bitmap> callback) {
        if (callback != null) {
            callback.onStart();
        }
        
        // 先获取免费token
        getBaiduToken(true, new ApiCallback<String>() {
            @Override
            public void onSuccess(String token) {
                performBaiduSegmentation(imagePath, token, callback, false);
            }
            
            @Override
            public void onError(String error) {
                // 免费token失败，尝试付费token
                getBaiduToken(false, new ApiCallback<String>() {
                    @Override
                    public void onSuccess(String token) {
                        performBaiduSegmentation(imagePath, token, callback, true);
                    }
                    
                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError("所有备用方案都失败了: " + error);
                            callback.onComplete();
                        }
                    }
                });
            }
        });
    }
    
    /**
     * 获取百度Token
     */
    @SuppressLint("CheckResult")
    private void getBaiduToken(boolean useFree, ApiCallback<String> callback) {
        RxHttp.get("").setUrl(Url.getTokenUrl(useFree))
                .asClass(TokenData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<TokenData>() {
                    @Override
                    public void accept(TokenData tokenData) throws Exception {
                        if (callback != null) {
                            callback.onSuccess(tokenData.access_token);
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("Token获取失败: " + throwable.getMessage());
                        }
                    }
                });
    }
    
    /**
     * 执行百度分割
     */
    @SuppressLint("CheckResult")
    private void performBaiduSegmentation(String imagePath, String token, ApiCallback<Bitmap> callback, boolean isPaid) {
        String bitmap64Str = Base64Utils.convertBitmapToBase64(BitmapFactory.decodeFile(imagePath));
        
        RxHttp.postForm(Url.segImg + "?access_token=" + token)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .add("image", bitmap64Str)
                .add("type", "foreground")
                .asClass(SegData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<SegData>() {
                    @Override
                    public void accept(SegData segData) throws Exception {
                        String foregroundData = segData.foreground;
                        try {
                            if (foregroundData == null || TextUtils.isEmpty(foregroundData)) {
                                if (!isPaid) {
                                    // 如果是免费版失败，尝试付费版
                                    getBaiduToken(false, new ApiCallback<String>() {
                                        @Override
                                        public void onSuccess(String paidToken) {
                                            performBaiduSegmentation(imagePath, paidToken, callback, true);
                                        }
                                        
                                        @Override
                                        public void onError(String error) {
                                            if (callback != null) {
                                                callback.onError("付费版本也失败了: " + error);
                                                callback.onComplete();
                                            }
                                        }
                                    });
                                } else {
                                    if (callback != null) {
                                        callback.onError("百度API返回空数据");
                                        callback.onComplete();
                                    }
                                }
                            } else {
                                Bitmap resultBitmap = Base64Utils.base64ToImage(foregroundData);
                                if (callback != null) {
                                    callback.onSuccess(resultBitmap);
                                    callback.onComplete();
                                }
                            }
                        } catch (Exception e) {
                            if (callback != null) {
                                callback.onError("图片处理失败: " + e.getMessage());
                                callback.onComplete();
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + throwable.getMessage());
                            callback.onComplete();
                        }
                    }
                });
    }
    
    /**
     * 本地图片处理备用方案
     * 当所有API都失败时的最后备用
     */
    public void localImageProcessing(String imagePath, ApiCallback<Bitmap> callback) {
        try {
            if (callback != null) {
                callback.onStart();
            }

            // 简单的本地处理：直接返回原图
            Bitmap originalBitmap = BitmapFactory.decodeFile(imagePath);
            if (originalBitmap != null) {
                if (callback != null) {
                    callback.onSuccess(originalBitmap);
                    callback.onComplete();
                }
            } else {
                if (callback != null) {
                    callback.onError("无法加载图片文件");
                    callback.onComplete();
                }
            }
        } catch (Exception e) {
            if (callback != null) {
                callback.onError("本地处理失败: " + e.getMessage());
                callback.onComplete();
            }
        }
    }

    /**
     * 本地证件照处理备用方案
     */
    public void localIdPhotoProcessing(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        localProcessingService.processIdPhoto(imagePath, picSize, callback);
    }

    /**
     * 本地排版照片处理备用方案
     */
    public void localLayoutPhotoProcessing(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        localProcessingService.processLayoutPhoto(imagePath, picSize, callback);
    }
    
    /**
     * 百度API人脸检测备用方案
     * @param context 上下文
     * @param imagePath 图片路径
     * @param callback 回调
     */
    public void baiduFaceDetection(Context context, String imagePath, ApiCallback<FaceDetectData> callback) {
        Log.d("FallbackService", "开始百度人脸检测备用方案");
        if (callback != null) {
            callback.onStart();
        }

        // 先获取免费token
        getBaiduToken(true, new ApiCallback<String>() {
            @Override
            public void onSuccess(String token) {
                performBaiduFaceDetection(imagePath, token, callback, false);
            }

            @Override
            public void onError(String error) {
                // 免费token失败，尝试付费token
                getBaiduToken(false, new ApiCallback<String>() {
                    @Override
                    public void onSuccess(String token) {
                        performBaiduFaceDetection(imagePath, token, callback, true);
                    }

                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError("百度人脸检测失败: " + error);
                            callback.onComplete();
                        }
                    }
                });
            }
        });
    }

    /**
     * 执行百度人脸检测
     */
    @SuppressLint("CheckResult")
    private void performBaiduFaceDetection(String imagePath, String token, ApiCallback<FaceDetectData> callback, boolean isPaid) {
        Bitmap sourceBitmap = BitmapFactory.decodeFile(imagePath);
        if (sourceBitmap == null) {
            if (callback != null) {
                callback.onError("无法加载图片文件");
                callback.onComplete();
            }
            return;
        }

        String bitmap64Str = Base64Utils.convertBitmapToBase64(sourceBitmap);

        RxHttp.postForm(Url.detectBody + "?access_token=" + token)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .add("image", bitmap64Str)
                .asClass(BodyData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<BodyData>() {
                    @Override
                    public void accept(BodyData data) throws Exception {
                        // 将百度BodyData转换为FaceDetectData格式
                        FaceDetectData faceData = convertBodyDataToFaceData(data);

                        if (data.person_num == -1 && !isPaid) {
                            // 免费版配额用完，尝试付费版
                            getBaiduToken(false, new ApiCallback<String>() {
                                @Override
                                public void onSuccess(String paidToken) {
                                    performBaiduFaceDetection(imagePath, paidToken, callback, true);
                                }

                                @Override
                                public void onError(String error) {
                                    if (callback != null) {
                                        callback.onError("付费版本也失败了: " + error);
                                        callback.onComplete();
                                    }
                                }
                            });
                        } else {
                            if (callback != null) {
                                callback.onSuccess(faceData);
                                callback.onComplete();
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("百度人脸检测网络请求失败: " + throwable.getMessage());
                            callback.onComplete();
                        }
                    }
                });
    }

    /**
     * 将百度BodyData转换为FaceDetectData格式
     */
    private FaceDetectData convertBodyDataToFaceData(BodyData bodyData) {
        FaceDetectData faceData = new FaceDetectData();
        faceData.face_count = bodyData.person_num;
        faceData.processing_time = 0.0; // 百度API不提供处理时间

        if (bodyData.person_num > 0 && bodyData.person_info != null && !bodyData.person_info.isEmpty()) {
            // 基于百度的body_parts信息构造人脸位置
            BodyData.BodyInfo bodyInfo = bodyData.person_info.get(0);
            if (bodyInfo.body_parts != null) {
                FaceDetectData.FaceLocation faceLocation = new FaceDetectData.FaceLocation();

                // 使用眼部和鼻子位置估算人脸区域
                BodyData.BodyPart.PartInfo leftEye = bodyInfo.body_parts.left_eye;
                BodyData.BodyPart.PartInfo rightEye = bodyInfo.body_parts.right_eye;
                BodyData.BodyPart.PartInfo nose = bodyInfo.body_parts.nose;

                if (leftEye != null && rightEye != null && nose != null) {
                    // 计算人脸区域的大致位置和大小
                    float eyeDistance = Math.abs(rightEye.x - leftEye.x);
                    float faceWidth = eyeDistance * 2.5f; // 估算人脸宽度
                    float faceHeight = faceWidth * 1.3f; // 估算人脸高度

                    float centerX = (leftEye.x + rightEye.x) / 2;
                    float centerY = (leftEye.y + rightEye.y + nose.y) / 3;

                    faceLocation.left = (int) (centerX - faceWidth / 2);
                    faceLocation.top = (int) (centerY - faceHeight / 2);
                    faceLocation.right = (int) (centerX + faceWidth / 2);
                    faceLocation.bottom = (int) (centerY + faceHeight / 2);
                } else {
                    // 如果关键点信息不完整，使用默认值
                    faceLocation.left = 100;
                    faceLocation.top = 100;
                    faceLocation.right = 300;
                    faceLocation.bottom = 360;
                }

                faceData.face_locations = java.util.Arrays.asList(faceLocation);
            }
        }

        return faceData;
    }

    /**
     * 完整的fallback链
     * 按优先级尝试所有可用的方案
     */
    public void executeFullFallbackChain(Context context, String imagePath, ApiCallback<Bitmap> callback) {
        // 第一步：尝试百度API
        baiduBodySegmentation(context, imagePath, new ApiCallback<Bitmap>() {
            @Override
            public void onSuccess(Bitmap bitmap) {
                // 百度API成功
                if (callback != null) {
                    callback.onSuccess(bitmap);
                    callback.onComplete();
                }
            }

            @Override
            public void onError(String error) {
                // 百度API失败，使用本地处理
                ToastUtils.showShort("使用本地处理作为最终备用...");
                localImageProcessing(imagePath, callback);
            }
        });
    }
}
