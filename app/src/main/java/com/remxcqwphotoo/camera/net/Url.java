package com.remxcqwphotoo.camera.net;

import rxhttp.wrapper.annotation.DefaultDomain;


public class Url {
    //当前key <EMAIL>
    static String APP_KEY = "GZ3j7qBdkXQi7AycP3e86Vn7";
    static String APP_KEY_MONEY = "bz7tYWQ1hcU52B7dreNFoGDq";
    //        "7o50OrpmLwD9s0G4mCplwLFp";
    static String APP_SEC = "5415WA7YONFDjfpd4WGPeN4WtwEhj0zV";
    static String APP_SEC_MONEY = "3f3wRGawuvVDLpyGGf5cMcNmSZdHefd7";
    // "GgUdqN9IkkxZz1jb0ppw6v0iNEvD97Rz";


    @DefaultDomain() //设置为默认域名
    public static String baseUrl = "https://aip.baidubce.com";
    public static final String genToken = String.format("https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s", APP_KEY, APP_SEC);
    public static final String detectBody = "https://aip.baidubce.com/rest/2.0/image-classify/v1/body_analysis";
    public static final String segImg = "https://aip.baidubce.com/rest/2.0/image-classify/v1/body_seg";

    // 新的人脸检测API
    public static final String faceDetectUrl = "https://api.juyingnj.com/face/detect";
    public static final String faceDetectToken = "aabbcc"; // Bearer token

    // ID照片生成API
    public static final String idPhotoUrl = "https://api.juyingnj.com/photo/idphoto";
    public static final String idPhotoToken = "aabbcc"; // Bearer token

    // 6寸排版照片API
    public static final String sixInchLayoutUrl = "https://api.juyingnj.com/photo/generate_layout_photos";
    public static final String sixInchLayoutToken = "aabbcc"; // Bearer token


    public static String getTokenUrl(boolean useFree) {
        if (useFree) {
            return String.format("https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s", APP_KEY, APP_SEC);
        }
        return String.format("https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s", APP_KEY_MONEY, APP_SEC_MONEY);
    }


}