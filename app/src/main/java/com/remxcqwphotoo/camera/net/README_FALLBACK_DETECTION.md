# 人脸检测Fallback策略文档

## 概述

为了提高人脸检测的成功率和用户体验，我们实现了一套完整的fallback策略。当新的人脸检测API失败时，系统会自动回退到百度API进行检测。

## 架构设计

### 1. 分层架构
- **第一层**: 新的人脸检测API (https://api.juyingnj.com/face/detect)
- **第二层**: 百度人脸检测API (百度body_analysis接口)
- **第三层**: 错误处理和用户提示

### 2. 核心组件

#### FallbackService
- 新增 `baiduFaceDetection()` 方法
- 实现百度API的token管理（免费版 -> 付费版）
- 提供BodyData到FaceDetectData的数据转换

#### IdPhotoManager  
- 新增 `detectFaceWithFallback()` 方法
- 统一管理fallback策略
- 提供简洁的API接口

#### DetectTask
- 更新 `startWithNewService()` 使用fallback策略
- 新增 `startWithFallback()` 便捷方法
- 保持向后兼容性

## 使用方法

### 推荐方式（使用fallback）
```java
DetectTask detectTask = new DetectTask(activity, imagePath);
detectTask.startWithFallback(new DetectTask.DetectBack() {
    @Override
    public void onSuccess(String path) {
        // 检测成功（可能来自新API或百度API）
    }
    
    @Override
    public void onFail(String msg) {
        // 所有检测方案都失败了
    }
});
```

### 使用Service架构（推荐）
```java
IdPhotoManager.getInstance().detectFaceWithFallback(context, imagePath, 
    new ApiCallback<FaceDetectData>() {
        @Override
        public void onSuccess(FaceDetectData faceData) {
            // 处理检测结果
        }
        
        @Override
        public void onError(String error) {
            // 处理错误
        }
    });
```

## 数据转换

### BodyData -> FaceDetectData
百度API返回的BodyData会被转换为标准的FaceDetectData格式：

1. **人脸数量**: `person_num` -> `face_count`
2. **人脸位置**: 基于眼部和鼻子位置估算人脸区域
3. **坐标系统**: 转换为标准的left/top/right/bottom格式

### 转换算法
- 眼距 × 2.5 = 人脸宽度
- 人脸宽度 × 1.3 = 人脸高度  
- 以双眼和鼻子的中心点为人脸中心

## 错误处理

### 分级错误处理
1. **新API失败**: 自动切换到百度API，用户无感知
2. **百度免费版失败**: 自动切换到百度付费版
3. **所有API失败**: 返回统一错误信息

### 用户体验
- 检测过程中显示统一的"检测中..."提示
- 失败时提供清晰的错误信息
- 不会向用户暴露技术细节

## 配置说明

### API配置
所有API配置统一在 `ApiConfig.java` 中管理：
- 新API的token和端点
- 百度API的密钥和端点

### Token管理
- 百度API支持免费版和付费版自动切换
- Token获取失败会自动重试

## 性能考虑

### 网络优化
- 优先使用更快的新API
- 百度API作为备用，减少不必要的请求

### 内存管理
- 及时释放Bitmap资源
- 避免重复的Base64转换

## 测试建议

### 测试场景
1. 新API正常工作
2. 新API失败，百度API成功
3. 所有API都失败
4. 网络异常情况
5. 图片格式异常

### 测试方法
```java
// 可以通过修改ApiConfig中的URL来模拟API失败
// 测试fallback是否正常工作
```

## 注意事项

1. **向后兼容**: 原有的检测方法仍然可用
2. **错误信息**: 统一错误信息格式，避免暴露内部实现
3. **资源管理**: 确保Dialog和网络请求的正确释放
4. **线程安全**: 所有回调都在主线程执行

## 未来扩展

可以考虑添加：
- 本地人脸检测作为最终fallback
- 检测结果缓存机制
- 更详细的性能监控
- A/B测试支持
