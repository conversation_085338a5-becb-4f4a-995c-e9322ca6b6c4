# 图片压缩算法日志检查指南

## 概述

我已经为 `detectFaceWithFallback` 流程添加了详细的日志，帮助你检查压缩算法是否符合预期。

## 日志标签

### 主要日志标签
- `IdPhotoManager`: 主流程控制和统一压缩
- `FaceDetectionService`: 新API的压缩逻辑
- `FallbackService`: 百度API的压缩逻辑

## 关键日志信息

### 1. 流程开始
```
IdPhotoManager: === 开始人脸检测Fallback流程 ===
IdPhotoManager: 输入图片路径: /path/to/image.jpg
IdPhotoManager: 策略: 统一压缩 → 新API → 百度API
```

### 2. 压缩阶段详细信息
```
IdPhotoManager: 步骤1: 开始统一图片压缩
IdPhotoManager: 开始统一压缩图片用于检测: /path/to/image.jpg
IdPhotoManager: 原始图片大小: 2.45 MB
IdPhotoManager: 原始图片尺寸: 3024x4032
IdPhotoManager: Luban压缩开始
IdPhotoManager: 压缩完成: /path/to/compressed_image.jpg
IdPhotoManager: 压缩后大小: 156.78 KB
IdPhotoManager: 压缩比例: 6.40%
IdPhotoManager: 压缩后尺寸: 1080x1440
```

### 3. 小图片跳过压缩
```
IdPhotoManager: 原始图片大小: 85.23 KB
IdPhotoManager: 图片小于100KB，跳过压缩直接使用原图
```

### 4. 检测流程
```
IdPhotoManager: 步骤2: 开始Fallback检测流程
IdPhotoManager: 步骤2.1: 尝试新API人脸检测
IdPhotoManager: 使用图片: /path/to/compressed_image.jpg
```

### 5. 成功场景
```
IdPhotoManager: 步骤2.1成功: 新API人脸检测成功
IdPhotoManager: 检测到人脸数量: 1
IdPhotoManager: 处理时间: 0.85秒
IdPhotoManager: === Fallback流程完成 ===
```

### 6. Fallback场景
```
IdPhotoManager: 步骤2.1失败: 新API人脸检测失败 - 网络错误
IdPhotoManager: 步骤2.2: 启用百度API备用方案
FallbackService: 开始百度人脸检测（跳过压缩）
IdPhotoManager: 步骤2.2成功: 百度API人脸检测成功
IdPhotoManager: === Fallback流程完成（使用备用方案） ===
```

## 压缩算法检查要点

### 1. 压缩触发条件
- **预期**: 图片大于100KB时触发压缩
- **检查**: 查看日志中的原始图片大小和是否有"跳过压缩"信息

### 2. 压缩效果
- **预期**: 大图片应该被显著压缩（通常压缩到几百KB以内）
- **检查**: 对比压缩前后的文件大小和压缩比例

### 3. 尺寸变化
- **预期**: 高分辨率图片应该被缩放到合理尺寸
- **检查**: 对比压缩前后的图片尺寸

### 4. 避免重复压缩
- **预期**: 整个fallback流程只压缩一次
- **检查**: 确保只有一个压缩日志序列，没有重复的压缩操作

## 使用 adb 查看日志

### 过滤相关日志
```bash
# 查看所有压缩相关日志
adb logcat -s IdPhotoManager FaceDetectionService FallbackService

# 只查看压缩算法相关
adb logcat | grep -E "(压缩|Luban|原始图片|压缩后)"

# 查看完整的fallback流程
adb logcat | grep -E "(=== |步骤[0-9])"
```

### 实时监控
```bash
# 清除旧日志并实时查看
adb logcat -c && adb logcat -s IdPhotoManager
```

## 预期的压缩行为

### 1. 大图片（>100KB）
```
原始: 2.5MB, 3024x4032 → 压缩后: ~150KB, ~1080x1440
压缩比例: 5-10%
```

### 2. 中等图片（100KB-1MB）
```
原始: 500KB, 1920x1080 → 压缩后: ~80KB, ~1080x608
压缩比例: 15-20%
```

### 3. 小图片（<100KB）
```
跳过压缩，直接使用原图
```

## 异常情况检查

### 1. 压缩失败
```
IdPhotoManager: 图片压缩失败: 文件读取错误
```

### 2. 文件不存在
```
IdPhotoManager: 原始图片文件不存在: /invalid/path.jpg
```

### 3. 内存不足
```
FaceDetectionService: 图片压缩失败: OutOfMemoryError
```

## 性能指标

### 1. 压缩时间
- **预期**: 大图片压缩时间 < 2秒
- **检查**: 从"压缩开始"到"压缩完成"的时间间隔

### 2. 文件大小控制
- **预期**: 压缩后图片 < 500KB
- **检查**: 压缩后的文件大小

### 3. 质量保持
- **预期**: 压缩后仍能正常进行人脸检测
- **检查**: 检测成功率是否正常

## 调试技巧

### 1. 测试不同大小的图片
- 小图片（<100KB）: 验证跳过压缩逻辑
- 中等图片（100KB-1MB）: 验证适度压缩
- 大图片（>2MB）: 验证强力压缩

### 2. 网络异常测试
- 断网测试fallback是否正常工作
- 检查是否避免了重复压缩

### 3. 内存压力测试
- 连续处理多张大图片
- 检查内存是否正常释放

## 常见问题排查

### Q: 为什么小图片也被压缩了？
A: 检查 `ignoreBy(100)` 设置和文件大小判断逻辑

### Q: 压缩后图片太大？
A: 检查Luban的压缩参数设置

### Q: 压缩时间太长？
A: 检查原始图片尺寸，考虑调整压缩策略

### Q: 检测失败率增加？
A: 检查压缩后的图片质量是否过低

## 优化建议

根据日志分析结果，可以考虑以下优化：

1. **调整压缩阈值**: 如果100KB阈值不合适
2. **优化压缩参数**: 根据实际效果调整Luban参数
3. **添加缓存**: 避免重复压缩相同图片
4. **异步优化**: 优化压缩的线程处理
