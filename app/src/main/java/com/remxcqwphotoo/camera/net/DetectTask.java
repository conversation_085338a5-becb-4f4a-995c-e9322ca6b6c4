package com.remxcqwphotoo.camera.net;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import androidx.lifecycle.LifecycleOwner;

import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.model.BodyData;
import com.remxcqwphotoo.camera.model.FaceDetectData;
import com.remxcqwphotoo.camera.model.TokenData;
import com.remxcqwphotoo.camera.service.IdPhotoManager;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;
import com.remxcqwphotoo.camera.v2.util.ToastUtil;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;
import com.rxjava.rxlife.RxLife;

import java.io.File;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
//import rxhttp.wrapper.param.RxHttp;
import rxhttp.RxHttp;
import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

public class DetectTask {
    private final String filePath;
    private Activity context;
    QMUITipDialog tipDialog;
    private DetectBack detectBack;

    public interface DetectBack {
        public void onSuccess(String path);

        public void onFail(String msg);
    }

    public DetectTask(Activity context, String filePath) {
        this.context = context;
        this.filePath = filePath;
    }

    public void start(DetectBack detectBack) {
        this.detectBack = detectBack;
        if (!TextUtils.isEmpty(filePath)) {
            compressImage(filePath);
        } else {
            if (detectBack != null) {
                detectBack.onFail("图片不能为空");
            }
        }
    }

    /**
     * 使用新API开始检测（跳过token获取，直接使用新的人脸检测接口）
     */
    public void startWithNewApi(DetectBack detectBack) {
        this.detectBack = detectBack;
        if (!TextUtils.isEmpty(filePath)) {
            compressImageForNewApi(filePath);
        } else {
            if (detectBack != null) {
                detectBack.onFail("图片不能为空");
            }
        }
    }

    private void compressImage(String resultPath) {
        tipDialog = new QMUITipDialog.Builder(context).setTipWord("图片检测中").setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create();
        Luban.with(context).load(new File(resultPath)).setCompressListener(new OnCompressListener() {
            @Override
            public void onStart() {
                tipDialog.show();
            }

            @Override
            public void onSuccess(File file) {
                getToken(file.getAbsolutePath());
            }

            @Override
            public void onError(Throwable e) {
                tipDialog.dismiss();
            }
        }).launch();
    }

    /**
     * 为新API压缩图片
     */
    private void compressImageForNewApi(String resultPath) {
        tipDialog = new QMUITipDialog.Builder(context).setTipWord("图片检测中").setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create();
        Luban.with(context).load(new File(resultPath)).setCompressListener(new OnCompressListener() {
            @Override
            public void onStart() {
                tipDialog.show();
            }

            @Override
            public void onSuccess(File file) {
                detectFaceWithNewApi(file.getAbsolutePath());
            }

            @Override
            public void onError(Throwable e) {
                tipDialog.dismiss();
                if (detectBack != null) {
                    detectBack.onFail("图片压缩失败");
                }
            }
        }).launch();
    }

    @SuppressLint("CheckResult")
    private void getToken(String sourcePath) {
        RxHttp.get("").setUrl(com.remxcqwphotoo.camera.net.Url.getTokenUrl(true)).asClass(TokenData.class).observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<TokenData>() {
                    @Override
                    public void accept(TokenData tokenData) throws Exception {
                        detectBody(tokenData.access_token, sourcePath);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        tipDialog.dismiss();
                    }
                });
    }

    @SuppressLint("CheckResult")
    private void getTokenMoney(String sourcePath) {
        RxHttp.get("").setUrl(Url.getTokenUrl(false)).asClass(TokenData.class).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<TokenData>() {
            @Override
            public void accept(TokenData tokenData) throws Exception {
                detectBodyMoney(tokenData.access_token, sourcePath);
            }
        }, new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {
                tipDialog.dismiss();
            }
        });
    }


    @SuppressLint("CheckResult")
    private void detectBody(String token, String sourcePath) {
        Bitmap sourceBitmap = BitmapFactory.decodeFile(sourcePath);
        if (sourceBitmap == null) {
            ToastUtils.showShort(sourcePath + "获取图片数据失败");
            tipDialog.dismiss();
            return;
        }
        String bitmap64Str = com.remxcqwphotoo.camera.net.Base64Utils.convertBitmapToBase64(sourceBitmap);
        RxHttp.postForm(com.remxcqwphotoo.camera.net.Url.detectBody + "?access_token=" + token)
                .addHeader("Content-Type", "application/x-www-form-urlencoded").add("image", bitmap64Str).
                asClass(BodyData.class).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<BodyData>() {
                    @Override
                    public void accept(BodyData data) throws Exception {
                        if (data.person_num == -1) {
                            getTokenMoney(sourcePath);
                            return;
                        }
                        tipDialog.dismiss();
                        if (data.person_num > 1) {
                            ToastUtil.showToast(context, "检测到多张人脸");
                        } else if (data.person_num == 1) {
                            BodyData.BodyPart partInfo = data.person_info.get(0).body_parts;
                            String msg = "";
                            if (partInfo.left_eye.score < 0.8 || partInfo.right_eye.score < 0.8 || Math.abs(partInfo.left_eye.y - partInfo.right_eye.y) > 20) {
                                msg = "眼部姿态不正确";
                            }
                            if (partInfo.nose.score < 0.8) {
                                msg = "人体姿态不正确";
                            }
                            if (TextUtils.isEmpty(msg)) {
                                detectBack.onSuccess(sourcePath);
                            } else {
                                String finalMsg = msg;
                                QMUIDialog msgDialog = new QMUIDialog.MessageDialogBuilder(context).addAction("是", new QMUIDialogAction.ActionListener() {
                                    @Override
                                    public void onClick(QMUIDialog dialog, int index) {
                                        dialog.dismiss();
                                        detectBack.onFail("");
                                    }
                                }).addAction("否", new QMUIDialogAction.ActionListener() {
                                    @Override
                                    public void onClick(QMUIDialog dialog, int index) {
                                        dialog.dismiss();
                                        detectBack.onSuccess(sourcePath);
                                    }
                                }).setMessage(msg + "是否继续重新操作?").create();
                                msgDialog.show();
                            }

                        } else {
                            ToastUtil.showToast(context, "没有检测到人脸");
                            detectBack.onFail("没有检测到人脸");
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        tipDialog.dismiss();
                    }
                });
    }


    @SuppressLint("CheckResult")
    private void detectBodyMoney(String token, String sourcePath) {
        Bitmap sourceBitmap = BitmapFactory.decodeFile(sourcePath);
        if (sourceBitmap == null) {
            ToastUtils.showShort(sourcePath + "获取图片数据失败");
            tipDialog.dismiss();
            return;
        }
        String bitmap64Str = com.remxcqwphotoo.camera.net.Base64Utils.convertBitmapToBase64(sourceBitmap);
        RxHttp.postForm(com.remxcqwphotoo.camera.net.Url.detectBody + "?access_token=" + token)
                .addHeader("Content-Type", "application/x-www-form-urlencoded").add("image", bitmap64Str).
                asClass(BodyData.class).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<BodyData>() {
                    @Override
                    public void accept(BodyData data) throws Exception {
                        tipDialog.dismiss();
                        if (data.person_num > 1) {
                            ToastUtil.showToast(context, "检测到多张人脸");
                        } else if (data.person_num == 1) {
                            BodyData.BodyPart partInfo = data.person_info.get(0).body_parts;
                            String msg = "";
                            if (partInfo.left_eye.score < 0.8 || partInfo.right_eye.score < 0.8 || Math.abs(partInfo.left_eye.y - partInfo.right_eye.y) > 20) {
                                msg = "眼部姿态不正确";
                            }
                            if (partInfo.nose.score < 0.8) {
                                msg = "人体姿态不正确";
                            }
                            if (TextUtils.isEmpty(msg)) {
                                detectBack.onSuccess(sourcePath);
                            } else {
                                String finalMsg = msg;
                                QMUIDialog msgDialog = new QMUIDialog.MessageDialogBuilder(context).addAction("是", new QMUIDialogAction.ActionListener() {
                                    @Override
                                    public void onClick(QMUIDialog dialog, int index) {
                                        dialog.dismiss();
                                        detectBack.onFail("");
                                    }
                                }).addAction("否", new QMUIDialogAction.ActionListener() {
                                    @Override
                                    public void onClick(QMUIDialog dialog, int index) {
                                        dialog.dismiss();
                                        detectBack.onSuccess(sourcePath);
                                    }
                                }).setMessage(msg + "是否继续重新操作?").create();
                                msgDialog.show();
                            }

                        } else {
                            ToastUtil.showToast(context, "没有检测到人脸");
                            detectBack.onFail("没有检测到人脸");
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        tipDialog.dismiss();
                    }
                });
    }

    /**
     * 使用新的人脸检测API进行检测
     */
    @SuppressLint("CheckResult")
    private void detectFaceWithNewApi(String sourcePath) {
        File imageFile = new File(sourcePath);
        if (!imageFile.exists()) {
            ToastUtils.showShort("图片文件不存在");
            tipDialog.dismiss();
            if (detectBack != null) {
                detectBack.onFail("图片文件不存在");
            }
            return;
        }

        RxHttp.postForm(Url.faceDetectUrl)
                .addHeader("Authorization", "Bearer " + Url.faceDetectToken)
                .addHeader("accept", "application/json")
                .addFile("file", imageFile)
                .asClass(FaceDetectData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<FaceDetectData>() {
                    @Override
                    public void accept(FaceDetectData data) throws Exception {
                        tipDialog.dismiss();
                        handleFaceDetectResult(data, sourcePath);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        tipDialog.dismiss();
                        ToastUtils.showShort("人脸检测失败: " + throwable.getMessage());
                        if (detectBack != null) {
                            detectBack.onFail("人脸检测失败");
                        }
                    }
                });
    }

    /**
     * 处理新API的人脸检测结果
     */
    private void handleFaceDetectResult(FaceDetectData data, String sourcePath) {
        if (data.face_count !=0) {
            // 获取第一个人脸位置信息
            FaceDetectData.FaceLocation faceLocation = data.face_locations.get(0);
            // 验证人脸区域是否有效
            if (!faceLocation.isValid()) {
                ToastUtil.showToast(context, "人脸区域无效");
                if (detectBack != null) {
                    detectBack.onFail("人脸区域无效");
                }
                return;
            }

            // 基于人脸区域进行基本的质量检查
            String msg = validateFaceQuality(faceLocation);

            if (TextUtils.isEmpty(msg)) {
                // 检测通过
                if (detectBack != null) {
                    detectBack.onSuccess(sourcePath);
                }
            } else {
                // 显示质量问题对话框
                QMUIDialog msgDialog = new QMUIDialog.MessageDialogBuilder(context)
                        .addAction("是", new QMUIDialogAction.ActionListener() {
                            @Override
                            public void onClick(QMUIDialog dialog, int index) {
                                dialog.dismiss();
                                if (detectBack != null) {
                                    detectBack.onFail("");
                                }
                            }
                        })
                        .addAction("否", new QMUIDialogAction.ActionListener() {
                            @Override
                            public void onClick(QMUIDialog dialog, int index) {
                                dialog.dismiss();
                                if (detectBack != null) {
                                    detectBack.onSuccess(sourcePath);
                                }
                            }
                        })
                        .setMessage(msg + "是否继续重新操作?")
                        .create();
                msgDialog.show();
            }
        } else {
            ToastUtil.showToast(context, "没有检测到人脸");
            if (detectBack != null) {
                detectBack.onFail("没有检测到人脸");
            }
        }
    }

    /**
     * 基于人脸位置信息进行质量验证
     * 由于新API只提供位置信息，我们进行基本的尺寸和位置检查
     */
    private String validateFaceQuality(FaceDetectData.FaceLocation faceLocation) {
        // 检查人脸区域大小是否合适
        int faceWidth = faceLocation.getWidth();
        int faceHeight = faceLocation.getHeight();

        // 人脸区域太小
        if (faceWidth < 100 || faceHeight < 100) {
            return "人脸区域过小，建议靠近拍摄";
        }

        // 检查人脸宽高比是否合理（正常人脸宽高比约为0.7-0.9）
        double aspectRatio = (double) faceWidth / faceHeight;
        if (aspectRatio < 0.5 || aspectRatio > 1.2) {
            return "人脸比例异常，请调整拍摄角度";
        }

        // 检查人脸是否过于靠近边缘
        if (faceLocation.left < 50 || faceLocation.top < 50) {
            return "人脸过于靠近边缘，请调整拍摄位置";
        }

        return ""; // 验证通过
    }
}
