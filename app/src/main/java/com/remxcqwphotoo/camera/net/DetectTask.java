package com.remxcqwphotoo.camera.net;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import androidx.lifecycle.LifecycleOwner;

import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.model.BodyData;
import com.remxcqwphotoo.camera.model.FaceDetectData;
import com.remxcqwphotoo.camera.model.TokenData;
import com.remxcqwphotoo.camera.service.IdPhotoManager;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;
import com.remxcqwphotoo.camera.v2.util.ToastUtil;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;
import com.rxjava.rxlife.RxLife;

import java.io.File;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
//import rxhttp.wrapper.param.RxHttp;
import rxhttp.RxHttp;
import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

public class DetectTask {
    private final String filePath;
    private Activity context;
    QMUITipDialog tipDialog;
    private DetectBack detectBack;

    public interface DetectBack {
        public void onSuccess(String path);

        public void onFail(String msg);
    }

    public DetectTask(Activity context, String filePath) {
        this.context = context;
        this.filePath = filePath;
    }

    /**
     * 使用新的Service架构进行人脸检测（带fallback）
     */
    public void startWithFallback(DetectBack detectBack) {
        this.detectBack = detectBack;
        if (TextUtils.isEmpty(filePath)) {
            if (detectBack != null) {
                detectBack.onFail("图片路径为空");
            }
            return;
        }

        // 显示加载对话框
        QMUITipDialog.Builder builder = new QMUITipDialog.Builder(context);
        tipDialog = builder.setTipWord("检测中...").setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create();
        tipDialog.show();

        // 使用新的Service进行人脸检测（带完整fallback策略）
        IdPhotoManager.getInstance().detectFaceWithFallback(context, filePath, new ApiCallback<FaceDetectData>() {
            @Override
            public void onSuccess(FaceDetectData faceData) {
                tipDialog.dismiss();
                handleFaceDetectResult(faceData, filePath);
            }

            @Override
            public void onError(String error) {
                tipDialog.dismiss();
                ToastUtils.showShort("人脸检测失败: " + error);
                if (detectBack != null) {
                    detectBack.onFail(error);
                }
            }
        });
    }

    /**
     * 处理新API的人脸检测结果
     */
    private void handleFaceDetectResult(FaceDetectData data, String sourcePath) {
        if (data.face_count != 0) {
            // 获取第一个人脸位置信息
            FaceDetectData.FaceLocation faceLocation = data.face_locations.get(0);
            // 验证人脸区域是否有效
            if (!faceLocation.isValid()) {
                ToastUtil.showToast(context, "人脸区域无效");
                if (detectBack != null) {
                    detectBack.onFail("人脸区域无效");
                }
                return;
            }

            // 基于人脸区域进行基本的质量检查
            String msg = validateFaceQuality(faceLocation);

            if (TextUtils.isEmpty(msg)) {
                // 检测通过
                if (detectBack != null) {
                    detectBack.onSuccess(sourcePath);
                }
            } else {
                // 显示质量问题对话框
                QMUIDialog msgDialog = new QMUIDialog.MessageDialogBuilder(context).addAction("是", new QMUIDialogAction.ActionListener() {
                    @Override
                    public void onClick(QMUIDialog dialog, int index) {
                        dialog.dismiss();
                        if (detectBack != null) {
                            detectBack.onFail("");
                        }
                    }
                }).addAction("否", new QMUIDialogAction.ActionListener() {
                    @Override
                    public void onClick(QMUIDialog dialog, int index) {
                        dialog.dismiss();
                        if (detectBack != null) {
                            detectBack.onSuccess(sourcePath);
                        }
                    }
                }).setMessage(msg + "是否继续重新操作?").create();
                msgDialog.show();
            }
        } else {
            ToastUtil.showToast(context, "没有检测到人脸");
            if (detectBack != null) {
                detectBack.onFail("没有检测到人脸");
            }
        }
    }

    /**
     * 基于人脸位置信息进行质量验证
     * 由于新API只提供位置信息，我们进行基本的尺寸和位置检查
     */
    private String validateFaceQuality(FaceDetectData.FaceLocation faceLocation) {
        // 检查人脸区域大小是否合适
        int faceWidth = faceLocation.getWidth();
        int faceHeight = faceLocation.getHeight();

        // 人脸区域太小
        if (faceWidth < 100 || faceHeight < 100) {
            return "人脸区域过小，建议靠近拍摄";
        }

        // 检查人脸宽高比是否合理（正常人脸宽高比约为0.7-0.9）
        double aspectRatio = (double) faceWidth / faceHeight;
        if (aspectRatio < 0.5 || aspectRatio > 1.2) {
            return "人脸比例异常，请调整拍摄角度";
        }

        // 检查人脸是否过于靠近边缘
        if (faceLocation.left < 50 || faceLocation.top < 50) {
            return "人脸过于靠近边缘，请调整拍摄位置";
        }

        return ""; // 验证通过
    }
}
