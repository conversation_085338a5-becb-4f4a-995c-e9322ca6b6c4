package com.remxcqwphotoo.camera;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;


import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.engine.ImageEngine;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.net.DetectTask;
import com.remxcqwphotoo.camera.utils.GlideEngine;
import com.remxcqwphotoo.camera.v2.util.ToastUtil;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.otaliastudios.cameraview.BitmapCallback;
import com.otaliastudios.cameraview.CameraException;
import com.otaliastudios.cameraview.CameraListener;
import com.otaliastudios.cameraview.CameraLogger;
import com.otaliastudios.cameraview.CameraOptions;
import com.otaliastudios.cameraview.CameraView;
import com.otaliastudios.cameraview.PictureResult;
import com.otaliastudios.cameraview.VideoResult;
import com.otaliastudios.cameraview.controls.Mode;
import com.otaliastudios.cameraview.controls.Preview;
import com.otaliastudios.cameraview.filter.Filters;
import com.otaliastudios.cameraview.frame.Frame;
import com.otaliastudios.cameraview.frame.FrameProcessor;


import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import io.reactivex.rxjava3.functions.Consumer;


public class CameraActivity extends AppCompatActivity implements View.OnClickListener, OptionView.Callback {

    private final static CameraLogger LOG = CameraLogger.create("DemoApp");
    private final static boolean USE_FRAME_PROCESSOR = false;
    private final static boolean DECODE_BITMAP = true;
    private static final int REQUEST_CODE_CHOOSE = 23;


    private CameraView camera;
    private ViewGroup controlPanel;
    private long mCaptureTime;

    private int mCurrentFilter = 0;
    private final Filters[] mAllFilters = Filters.values();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE);
            setContentView(R.layout.activity_camera);
            getWindow().addFlags(Window.FEATURE_NO_TITLE);
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
            CameraLogger.setLogLevel(CameraLogger.LEVEL_VERBOSE);


            camera = findViewById(R.id.camera);
            camera.setLifecycleOwner(this);
            camera.addCameraListener(new Listener());

            if (USE_FRAME_PROCESSOR) {
                camera.addFrameProcessor(new FrameProcessor() {
                    private long lastTime = System.currentTimeMillis();

                    @Override
                    public void process(@NonNull Frame frame) {
                        long newTime = frame.getTime();
                        long delay = newTime - lastTime;
                        lastTime = newTime;
                        LOG.e("Frame delayMillis:", delay, "FPS:", 1000 / delay);
                        if (DECODE_BITMAP) {
                            YuvImage yuvImage = new YuvImage(frame.getData(), ImageFormat.NV21,
                                    frame.getSize().getWidth(),
                                    frame.getSize().getHeight(),
                                    null);
                            ByteArrayOutputStream jpegStream = new ByteArrayOutputStream();
                            yuvImage.compressToJpeg(new Rect(0, 0,
                                    frame.getSize().getWidth(),
                                    frame.getSize().getHeight()), 100, jpegStream);
                            byte[] jpegByteArray = jpegStream.toByteArray();
                            Bitmap bitmap = BitmapFactory.decodeByteArray(jpegByteArray, 0, jpegByteArray.length);
                            //noinspection ResultOfMethodCallIgnored
                            bitmap.toString();
                        }
                    }
                });
            }

//        findViewById(R.id.edit).setOnClickListener(this);
//        findViewById(R.id.capturePicture).setOnClickListener(this);
            findViewById(R.id.capturePictureSnapshot).setOnClickListener(this);
//        findViewById(R.id.captureVideo).setOnClickListener(this);
            findViewById(R.id.toggleCamera).setOnClickListener(this);
            findViewById(R.id.close_btn).setOnClickListener(this);
            findViewById(R.id.chooseImage).setOnClickListener(this);
//            findViewById(R.id.beauty).setOnClickListener(this);
//        findViewById(R.id.changeFilter).setOnClickListener(this);

            controlPanel = findViewById(R.id.controls);
            ViewGroup group = (ViewGroup) controlPanel.getChildAt(0);

            List<Option<?>> options = Arrays.asList(
                    // Layout
                    new Option.Width(), new Option.Height(),
                    // Engine and preview
                    new Option.Mode(), new Option.Engine(), new Option.Preview(),
                    // Some controls
                    new Option.Flash(), new Option.WhiteBalance(), new Option.Hdr(),
                    new Option.PictureMetering(), new Option.PictureSnapshotMetering(),
                    // Video recording
                    new Option.VideoCodec(), new Option.Audio(),
                    // Gestures
                    new Option.Pinch(), new Option.HorizontalScroll(), new Option.VerticalScroll(),
                    new Option.Tap(), new Option.LongTap(),
                    // Watermarks
                    // Other
                    new Option.Grid(), new Option.GridColor(), new Option.UseDeviceOrientation()
            );
            List<Boolean> dividers = Arrays.asList(
                    false, true,
                    false, false, true,
                    false, false, false, false, true,
                    false, true,
                    false, false, false, false, true,
                    false, false, true,
                    false, false, true
            );
            for (int i = 0; i < options.size(); i++) {
                OptionView view = new OptionView(this);
                //noinspection unchecked
                view.setOption(options.get(i), this);
                view.setHasDivider(dividers.get(i));
                group.addView(view,
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT);
            }

            controlPanel.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    BottomSheetBehavior b = BottomSheetBehavior.from(controlPanel);
                    b.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    private void message(@NonNull String content, boolean important) {
//        if (important) {
//            LOG.w(content);
//            Toast.makeText(this, content, Toast.LENGTH_LONG).show();
//        } else {
//            LOG.i(content);
//            Toast.makeText(this, content, Toast.LENGTH_SHORT).show();
//        }
    }

    private class Listener extends CameraListener {

        @Override
        public void onCameraOpened(@NonNull CameraOptions options) {
            ViewGroup group = (ViewGroup) controlPanel.getChildAt(0);
            for (int i = 0; i < group.getChildCount(); i++) {
                OptionView view = (OptionView) group.getChildAt(i);
                view.onCameraOpened(camera, options);
            }
        }

        @Override
        public void onCameraError(@NonNull CameraException exception) {
            super.onCameraError(exception);
            message("Got CameraException #" + exception.getReason(), true);
        }

        @Override
        public void onPictureTaken(@NonNull PictureResult result) {
            super.onPictureTaken(result);
            result.toBitmap(1080, 1080, new BitmapCallback() {
                @Override
                public void onBitmapReady(@Nullable Bitmap bitmap) {
                    File file = ImageUtils.save2Album(bitmap, Bitmap.CompressFormat.JPEG);
                    detectImage(file.getPath());
                }
            });
        }

        @Override
        public void onVideoTaken(@NonNull VideoResult result) {
            super.onVideoTaken(result);
            LOG.w("onVideoTaken called! Launching activity.");
            VideoPreviewActivity.setVideoResult(result);
            Intent intent = new Intent(CameraActivity.this, VideoPreviewActivity.class);
            startActivity(intent);
            LOG.w("onVideoTaken called! Launched activity.");
        }

        @Override
        public void onVideoRecordingStart() {
            super.onVideoRecordingStart();
            LOG.w("onVideoRecordingStart!");
        }

        @Override
        public void onVideoRecordingEnd() {
            super.onVideoRecordingEnd();
            message("Video taken. Processing...", false);
            LOG.w("onVideoRecordingEnd!");
        }

        @Override
        public void onExposureCorrectionChanged(float newValue, @NonNull float[] bounds, @Nullable PointF[] fingers) {
            super.onExposureCorrectionChanged(newValue, bounds, fingers);
            message("Exposure correction:" + newValue, false);
        }

        @Override
        public void onZoomChanged(float newValue, @NonNull float[] bounds, @Nullable PointF[] fingers) {
            super.onZoomChanged(newValue, bounds, fingers);
            message("Zoom:" + newValue, false);
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.capturePictureSnapshot:
                capturePictureSnapshot();
                break;
            case R.id.toggleCamera:
                toggleCamera();
                break;
            case R.id.chooseImage:
                chooseImage();
                break;
//            case R.id.beauty:
//                changeCurrentFilter();
//                break;
            case R.id.close_btn:
                onBackPressed();
                break;
        }
    }


    @SuppressLint("CheckResult")
    private void chooseImage() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage()).setMaxSelectNum(1).setImageEngine((ImageEngine) GlideEngine.Companion.createGlideEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {

                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String picPath = result.get(0).getRealPath();
                        detectImage(picPath);
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }


    @Override
    public void onBackPressed() {
        BottomSheetBehavior b = BottomSheetBehavior.from(controlPanel);
        if (b.getState() != BottomSheetBehavior.STATE_HIDDEN) {
            b.setState(BottomSheetBehavior.STATE_HIDDEN);
            return;
        }
        super.onBackPressed();
    }

    private void edit() {
        BottomSheetBehavior b = BottomSheetBehavior.from(controlPanel);
        b.setState(BottomSheetBehavior.STATE_COLLAPSED);
    }

    private void capturePicture() {
        if (camera.getMode() == Mode.VIDEO) {
            message("Can't take HQ pictures while in VIDEO mode.", false);
            return;
        }
        if (camera.isTakingPicture()) return;
        mCaptureTime = System.currentTimeMillis();
        // message("Capturing picture...", false);
        camera.takePicture();
    }

    private void capturePictureSnapshot() {
        if (camera.isTakingPicture()) return;
        if (camera.getPreview() != Preview.GL_SURFACE) {
            message("Picture snapshots are only allowed with the GL_SURFACE preview.", true);
            return;
        }
        mCaptureTime = System.currentTimeMillis();
        //   message("Capturing picture snapshot...", false);
        camera.takePictureSnapshot();
    }

    private void captureVideo() {
        if (camera.getMode() == Mode.PICTURE) {
            message("Can't record HQ videos while in PICTURE mode.", false);
            return;
        }
        if (camera.isTakingPicture() || camera.isTakingVideo()) return;
        message("Recording for 5 seconds...", true);
        camera.takeVideo(new File(getFilesDir(), "video.mp4"), 5000);
    }

    private void captureVideoSnapshot() {
        if (camera.isTakingVideo()) {
            message("Already taking video.", false);
            return;
        }
        if (camera.getPreview() != Preview.GL_SURFACE) {
            message("Video snapshots are only allowed with the GL_SURFACE preview.", true);
            return;
        }
        message("Recording snapshot for 5 seconds...", true);
        camera.takeVideoSnapshot(new File(getFilesDir(), "video.mp4"), 5000);
    }

    private void toggleCamera() {
        if (camera.isTakingPicture() || camera.isTakingVideo()) return;
        switch (camera.toggleFacing()) {
            case BACK:
                // message("Switched to back camera!", false);
                break;

            case FRONT:
                // message("Switched to front camera!", false);
                break;
        }
    }

    private void changeCurrentFilter() {
        if (camera.getPreview() != Preview.GL_SURFACE) {
            message("Filters are supported only when preview is Preview.GL_SURFACE.", true);
            return;
        }
        if (mCurrentFilter < mAllFilters.length - 1) {
            mCurrentFilter++;
        } else {
            mCurrentFilter = 0;
        }
        Filters filter = mAllFilters[mCurrentFilter];
        camera.setFilter(filter.newInstance());
//        message(filter.toString(), false);

        // To test MultiFilter:
        // DuotoneFilter duotone = new DuotoneFilter();
        // duotone.setFirstColor(Color.RED);
        // duotone.setSecondColor(Color.GREEN);
        // camera.setFilter(new MultiFilter(duotone, filter.newInstance()));
    }

    @Override
    public <T> boolean onValueChanged(@NonNull Option<T> option, @NonNull T value, @NonNull String name) {
        if ((option instanceof Option.Width || option instanceof Option.Height)) {
            Preview preview = camera.getPreview();
            boolean wrapContent = (Integer) value == ViewGroup.LayoutParams.WRAP_CONTENT;
            if (preview == Preview.SURFACE && !wrapContent) {
                message("The SurfaceView preview does not support width or height changes. " +
                        "The view will act as WRAP_CONTENT by default.", true);
                return false;
            }
        }
        option.set(camera, value);
        BottomSheetBehavior b = BottomSheetBehavior.from(controlPanel);
        b.setState(BottomSheetBehavior.STATE_HIDDEN);
        message("Changed " + option.getName() + " to " + name, false);
        return true;
    }


    private void detectImage(String filePath) {
        new DetectTask(this, filePath).startWithFallback(new DetectTask.DetectBack() {
            @Override
            public void onSuccess(String path) {
                PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");
                Intent intent = new Intent(CameraActivity.this, PicturePreviewActivity.class);
                intent.putExtra(PicturePreviewActivity.PICK_IMG, path);
                intent.putExtra("SIZE", picSize);
                startActivity(intent);
            }

            @Override
            public void onFail(String msg) {
                if (!TextUtils.isEmpty(msg)) {
                    ToastUtil.showToast(CameraActivity.this, msg);
                }
            }
        });
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean valid = true;
        for (int grantResult : grantResults) {
            valid = valid && grantResult == PackageManager.PERMISSION_GRANTED;
        }
        if (valid && !camera.isOpened()) {
            camera.open();
        }
    }

    //endregion
}
