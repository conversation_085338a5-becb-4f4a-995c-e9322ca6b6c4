package com.remxcqwphotoo.camera;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.graphics.BitmapRegionDecoder;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.viewpager.widget.ViewPager;

import com.blankj.utilcode.util.ImageUtils;

import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.FileUtils;
import com.remxcqwphotoo.camera.ad.AdNet;
import com.remxcqwphotoo.camera.ad.AdSdk;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.model.SixInchLayoutData;
import com.remxcqwphotoo.camera.net.Base64Utils;
import com.remxcqwphotoo.camera.net.Url;
import com.remxcqwphotoo.camera.service.IdPhotoManager;
import com.remxcqwphotoo.camera.service.LayoutPhotoService;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;
import com.remxcqwphotoo.camera.v2.ShopListActivity;

import com.qmuiteam.qmui.widget.QMUITabSegment;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;


public class SavePicActivity extends AppCompatActivity implements View.OnClickListener {


    private String filePath;
    private Bitmap originBitmap = null;
    private float originHeight;
    private float originWidth;
    private Bitmap destBitmap;
    private int saveType = -1;
    private PicSize picSize;
    private ViewPager viewPager;
    private QMUITabSegment mTabSegment;
    private QMUIRoundButton btnSave;
    private int clickCnt = 0;
    private QMUITipDialog loadingDialog;
    private String lastSavedImagePath; // 保存最后生成的图片路径，用于分享

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE);
        setContentView(R.layout.activity_save_pic);
        filePath = getIntent().getStringExtra(PicturePreviewActivity.PICK_IMG);
        picSize = (PicSize) getIntent().getSerializableExtra("SIZE");
        originBitmap = BitmapFactory.decodeFile(filePath);
        destBitmap = originBitmap;
        originWidth = originBitmap.getWidth();
        originHeight = originBitmap.getHeight();
        initView();
        initTabAndPage();
        AdSdk.getInstance().showNewInter(this);
    }

    private void initView() {
        QMUITopBarLayout topBar = findViewById(R.id.top_bar);
        topBar.setTitle("电子排版");
        topBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        // 添加分享按钮到导航栏右侧
        topBar.addRightImageButton(R.drawable.ic_share_black, R.id.btn_share).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                shareCurrentPhoto();
            }
        });
        mTabSegment = findViewById(R.id.tab_segment);
        viewPager = findViewById(R.id.pic_viewpager);
        btnSave = findViewById(R.id.btn_save);
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 根据当前tab类型保存照片
                int currentTab = viewPager.getCurrentItem();
                savePhotoByTabType(currentTab);

                AdSdk.getInstance().showReward(SavePicActivity.this, new AdSdk.OnRewardBack() {
                    @Override
                    public void onReward(int type) {
                        // 奖励广告完成，保存逻辑已经在上面执行
                    }
                },"是否解锁次功能？");

            }
        });
    }

    private void showLoadingDialog() {
        if (loadingDialog == null) {
            loadingDialog = new QMUITipDialog.Builder(this)
                    .setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING)
                    .setTipWord("正在生成排版照片...")
                    .create();
        }
        loadingDialog.show();
    }

    /**
     * 根据tab类型保存照片
     * @param tabIndex 0=电子照, 1=排版照
     */
    private void savePhotoByTabType(int tabIndex) {
        if (tabIndex == 0) {
            // Tab 0: 电子照 - 保存单张证件照
            saveType = 1;
            saveElectronicPhoto();
        } else if (tabIndex == 1) {
            // Tab 1: 排版照 - 保存6寸排版照
            saveType = 2;
            saveLayoutPhoto();
        }
    }

    /**
     * 保存电子照（单张证件照）
     */
    private void saveElectronicPhoto() {
        // 获取当前Fragment中的照片
        Bitmap photoToSave = getCurrentTabBitmap();
        if (photoToSave != null) {
            destBitmap = photoToSave;
            saveFile();
        } else {
            // 如果Fragment中没有照片，使用原始照片
            destBitmap = originBitmap;
            saveFile();
        }
    }

    /**
     * 保存排版照（6寸排版照）
     */
    private void saveLayoutPhoto() {
        generateLayoutWithNewService();
    }

    /**
     * 使用新的Service架构生成排版照片
     */
    private void generateLayoutWithNewService() {

        // 排版照片，使用带fallback的Service生成
        IdPhotoManager.getInstance().generateLayoutPhotoWithFallback(this, filePath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                showLoadingDialog();
                // loading dialog已经显示
            }

            @Override
            public void onSuccess(Bitmap layoutBitmap) {
                if (loadingDialog != null) {
                    loadingDialog.dismiss();
                }
                destBitmap = layoutBitmap;
                saveFile();
            }

            @Override
            public void onError(String error) {
                formatImageFallback();
            }

            @Override
            public void onComplete() {
                if (loadingDialog != null) {
                    loadingDialog.dismiss();
                }
                // 处理完成
            }
        });
    }

    private void formatMultiImage() {
        int space = 20;
        int bH = destBitmap.getHeight();
        int bW = destBitmap.getWidth();
        int totalW = bW * 4 + space * 5;
        int totalH = bH * 2 + space * 3;
        Bitmap canvasBitmap = Bitmap.createBitmap(totalW, totalH, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(canvasBitmap);
        Paint paint = new Paint();
        paint.setColor(Color.parseColor("#ffffff"));
        paint.setStyle(Paint.Style.FILL);
        canvas.drawRect(0, 0, totalW, totalH, paint);
        for (int i = 0; i < 8; i++) {
            if (i < 4) {
                canvas.drawBitmap(destBitmap, new Rect(0, 0, bW, bH), new Rect(space * (i + 1) + bW * i, space, space * (i + 1) + bW * i + bW, bH + space), paint);
            } else {
                canvas.drawBitmap(destBitmap, new Rect(0, 0, bW, bH), new Rect(space * (i - 3) + bW * (i - 4), bH + 2 * space, space * (i - 3) + bW * (i - 4) + bW, 2 * bH + 2 * space), paint);
            }
        }
        destBitmap = canvasBitmap;
    }

    /**
     * 备用方案：使用原有的6寸排版API
     */

    // 备用的本地处理方法
    private void formatImageFallback() {
        try {
            float height = picSize.height;
            float width = picSize.width;
            float minScale = Math.min(originHeight / height, originWidth / width);
            if (minScale < 1) {
                Matrix matrix = new Matrix();
                matrix.postScale(1 / minScale, 1 / minScale);
                Bitmap newBitmap = Bitmap.createBitmap(originBitmap, 0, 0, originBitmap.getWidth(), originBitmap.getHeight(), matrix, true);
                String tempPath = ImageUtils.save2Album(destBitmap, Bitmap.CompressFormat.JPEG).getPath();

                originHeight = newBitmap.getHeight();
                originWidth = newBitmap.getWidth();
                BitmapRegionDecoder decoder = BitmapRegionDecoder.newInstance(tempPath, true);
                float scaleWidth = width * minScale;
                float scaleHeight = height * minScale;
                int offsetX = Math.round((originWidth - scaleWidth) / 2);
                int offsetY = Math.round((originHeight - scaleHeight) / 2);
                Rect cropRect = new Rect(0, 0, Math.round(scaleWidth), Math.round(scaleHeight));
                cropRect.offsetTo(offsetX, offsetY);
                destBitmap = decoder.decodeRegion(cropRect, new BitmapFactory.Options());
            } else {
                BitmapRegionDecoder decoder = BitmapRegionDecoder.newInstance(filePath, true);
                float scaleWidth = width * minScale;
                float scaleHeight = height * minScale;
                int offsetX = Math.round((originWidth - scaleWidth) / 2);
                int offsetY = Math.round((originHeight - scaleHeight) / 2);
                Rect cropRect = new Rect(0, 0, Math.round(scaleWidth), Math.round(scaleHeight));
                cropRect.offsetTo(offsetX, offsetY);
                destBitmap = decoder.decodeRegion(cropRect, new BitmapFactory.Options());
                Matrix matrix = new Matrix();
                matrix.postScale(width / destBitmap.getWidth(), height / destBitmap.getHeight());
                destBitmap = Bitmap.createBitmap(destBitmap, 0, 0, destBitmap.getWidth(), destBitmap.getHeight(), matrix, true);
            }
            if (saveType == 2) {
                formatMultiImage();
            }
            saveFile();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onClick(View view) {

    }


    @SuppressLint("CheckResult")
    private void saveFile() {
        try {
            File savedFile = ImageUtils.save2Album(destBitmap, Bitmap.CompressFormat.JPEG);
            lastSavedImagePath = savedFile.getAbsolutePath(); // 保存图片路径用于分享
            QMUITipDialog tipDialog = new QMUITipDialog.Builder(this).setIconType(QMUITipDialog.Builder.ICON_TYPE_INFO).setTipWord("保存成功请在相册查找").create();
            tipDialog.show();
            Observable.timer(1500, TimeUnit.MILLISECONDS).observeOn(AndroidSchedulers.mainThread()).
                    subscribe(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            tipDialog.dismiss();
                                            // openShopList();
                                        }
                                    });
                                }
                            });
                        }
                    });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void openShopList() {
        Intent intent = new Intent(this, ShopListActivity.class);
        startActivity(intent);
    }

    private void initTabAndPage() {
        PicPreviewFragmentAdapter adapter = new PicPreviewFragmentAdapter(getSupportFragmentManager(), picSize, filePath);
        viewPager.setAdapter(adapter);
        mTabSegment.setHasIndicator(true);
        mTabSegment.setDefaultSelectedColor(getResources().getColor(R.color.home_tab_selected_color));
        mTabSegment.addTab(new QMUITabSegment.Tab(getString(R.string.tabSegment_pre_1_title)));
        mTabSegment.addTab(new QMUITabSegment.Tab(getString(R.string.tabSegment_pre_2_title)));
        mTabSegment.setupWithViewPager(viewPager, false);
        mTabSegment.setMode(QMUITabSegment.MODE_FIXED);
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    /**
     * 获取当前tab中的照片
     */
    private Bitmap getCurrentTabBitmap() {
        try {
            // 通过ViewPager获取当前Fragment
            PicPreviewFragmentAdapter adapter = (PicPreviewFragmentAdapter) viewPager.getAdapter();
            if (adapter != null) {
                int currentItem = viewPager.getCurrentItem();
                // 这里需要从Fragment中获取当前显示的照片
                // 由于Fragment的具体实现，我们使用备用方案
                return getCurrentDisplayBitmap();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return getCurrentDisplayBitmap();
    }

    /**
     * 分享当前照片
     */
    private void shareCurrentPhoto() {
        // 根据当前tab类型分享不同的照片
        int currentTab = viewPager.getCurrentItem();
        sharePhotoByTabType(currentTab);
    }

    /**
     * 根据tab类型分享照片
     * @param tabIndex 0=电子照, 1=排版照
     */
    private void sharePhotoByTabType(int tabIndex) {
        Bitmap bitmapToShare = null;
        String shareText = "";

        if (tabIndex == 0) {
            // Tab 0: 电子照 - 分享单张证件照
            bitmapToShare = getCurrentTabBitmap();
            shareText = "分享我的电子证件照";
        } else if (tabIndex == 1) {
            // Tab 1: 排版照 - 分享6寸排版照
            bitmapToShare = getCurrentDisplayBitmap();
            shareText = "分享我的6寸排版照";
        }

        if (bitmapToShare == null) {
            ToastUtils.showShort("请先生成照片再分享");
            return;
        }

        sharePhotoWithText(bitmapToShare, shareText);
    }

    /**
     * 分享照片（带自定义文本）
     */
    private void sharePhotoWithText(Bitmap bitmap, String shareText) {
        try {
            // 创建临时文件用于分享
            File tempFile = createTempShareFile(bitmap);
            if (tempFile != null) {
                shareImageFileWithText(tempFile, shareText);
            } else {
                ToastUtils.showShort("分享失败，请重试");
            }
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showShort("分享失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前显示的照片
     */
    private Bitmap getCurrentDisplayBitmap() {
        // 如果有已保存的照片，优先使用
        if (destBitmap != null) {
            return destBitmap;
        }

        // 否则使用原始照片
        if (originBitmap != null) {
            return originBitmap;
        }

        // 最后尝试从文件路径加载
        if (filePath != null) {
            return BitmapFactory.decodeFile(filePath);
        }

        return null;
    }

    /**
     * 创建临时分享文件
     */
    private File createTempShareFile(Bitmap bitmap) {
        try {
            // 创建临时文件
            File shareDir = new File(getExternalCacheDir(), "share");
            if (!shareDir.exists()) {
                shareDir.mkdirs();
            }

            String fileName = "share_photo_" + System.currentTimeMillis() + ".jpg";
            File tempFile = new File(shareDir, fileName);

            // 保存bitmap到临时文件
             ImageUtils.save(bitmap, tempFile, Bitmap.CompressFormat.JPEG);
             return  tempFile;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 分享图片文件（带自定义文本）
     */
    private void shareImageFileWithText(File imageFile, String shareText) {
        try {
            // 使用FileProvider获取Uri
            Uri imageUri = androidx.core.content.FileProvider.getUriForFile(
                this,
                getPackageName() + ".fileprovider",
                imageFile
            );

            // 创建分享Intent
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("image/*");
            shareIntent.putExtra(Intent.EXTRA_STREAM, imageUri);
            shareIntent.putExtra(Intent.EXTRA_TEXT, shareText);
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            // 启动分享选择器
            Intent chooser = Intent.createChooser(shareIntent, "分享照片到");
            startActivity(chooser);

        } catch (Exception e) {
            e.printStackTrace();
            // 如果FileProvider失败，尝试直接分享文件路径
            shareImageWithPathAndText(imageFile.getAbsolutePath(), shareText);
        }
    }

    /**
     * 备用分享方法：直接使用文件路径（带自定义文本）
     */
    private void shareImageWithPathAndText(String imagePath, String shareText) {
        try {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("image/*");
            shareIntent.putExtra(Intent.EXTRA_STREAM, Uri.parse("file://" + imagePath));
            shareIntent.putExtra(Intent.EXTRA_TEXT, shareText);

            Intent chooser = Intent.createChooser(shareIntent, "分享照片到");
            startActivity(chooser);
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showShort("分享失败，请检查应用权限");
        }
    }

    /**
     * 分享已保存的照片（如果存在）
     */
    private void shareLastSavedPhoto() {
        if (lastSavedImagePath != null && FileUtils.isFileExists(lastSavedImagePath)) {
            // 根据当前tab确定分享文本
            int currentTab = viewPager.getCurrentItem();
            String shareText = currentTab == 0 ? "分享我的电子证件照" : "分享我的6寸排版照";
            shareImageWithPathAndText(lastSavedImagePath, shareText);
        } else {
            shareCurrentPhoto();
        }
    }

}
