package com.remxcqwphotoo.camera;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BitmapRegionDecoder;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.model.SixInchLayoutData;
import com.remxcqwphotoo.camera.net.Base64Utils;
import com.remxcqwphotoo.camera.net.Url;
import com.remxcqwphotoo.camera.service.IdPhotoManager;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;
import com.remxcqwphotoo.camera.v2.base.BaseFragment;

import java.io.File;
import java.io.IOException;

import android.annotation.SuppressLint;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;

public class SinglePicFragment extends BaseFragment {
    private ImageView cropImageView;
    private String filePath;
    private Bitmap originBitmap = null;
    private float originHeight;
    private float originWidth;
    private Bitmap destBitmap;
    private int saveType = -1;
    private PicSize picSize;

    public static SinglePicFragment newInstance(int type, String filePath, PicSize picSize) {
        Bundle arg = new Bundle();
        arg.putInt("POS", type);
        arg.putString(PicturePreviewActivity.PICK_IMG, filePath);
        arg.putSerializable("SIZE", picSize);
        SinglePicFragment singlePicFragment = new SinglePicFragment();
        singlePicFragment.setArguments(arg);
        return singlePicFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        filePath = getArguments().getString(PicturePreviewActivity.PICK_IMG);
        saveType = getArguments().getInt("POS");
        picSize = (PicSize) getArguments().getSerializable("SIZE");

    }

    @Override
    protected View onCreateView() {
        return LayoutInflater.from(getContext()).inflate(R.layout.fragment_layout_single_pic, null, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        originBitmap = BitmapFactory.decodeFile(filePath);
        destBitmap = originBitmap;
        originWidth = originBitmap.getWidth();
        originHeight = originBitmap.getHeight();
        cropImageView = view.findViewById(R.id.crop_image);
        cropImageView.setImageBitmap(originBitmap);
        if (saveType == 2) {
            generatePreviewWithApi();
        }
        // 使用API生成预览
    }

    @SuppressLint("CheckResult")
    private void generatePreviewWithApi() {
        if (saveType == 1) {
            // 单张照片，直接显示原图
            formatImage();
            return;
        }

        // 排版照片，使用Service生成预览
        IdPhotoManager.getInstance().generateLayoutPreview(filePath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onSuccess(Bitmap previewBitmap) {
                cropImageView.setImageBitmap(previewBitmap);
                destBitmap = previewBitmap;

                // 如果是排版模式，旋转90度显示
                if (saveType == 2) {
                    cropImageView.setRotation(90);
                }
            }

            @Override
            public void onError(String error) {
                // 新Service失败，使用原有的本地处理逻辑作为备用
                formatImageFallback();
            }
        });
    }

    // 备用的本地处理方法
    private void formatImageFallback() {
        formatImage();
    }

    private void formatMultiImage() {
        int space = 20;
        int bH = destBitmap.getHeight();
        int bW = destBitmap.getWidth();
        int totalW = bW * 4 + space * 5;
        int totalH = bH * 2 + space * 3;
        Bitmap canvasBitmap = Bitmap.createBitmap(totalW, totalH, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(canvasBitmap);
        Paint paint = new Paint();
        paint.setColor(Color.parseColor("#ffffff"));
        paint.setStyle(Paint.Style.FILL);
        canvas.drawRect(0, 0, totalW, totalH, paint);
        for (int i = 0; i < 8; i++) {
            if (i < 4) {
                canvas.drawBitmap(destBitmap, new Rect(0, 0, bW, bH), new Rect(space * (i + 1) + bW * i, space, space * (i + 1) + bW * i + bW, bH + space), paint);
            } else {
                canvas.drawBitmap(destBitmap, new Rect(0, 0, bW, bH), new Rect(space * (i - 3) + bW * (i - 4), bH + 2 * space, space * (i - 3) + bW * (i - 4) + bW, 2 * bH + 2 * space), paint);
            }
        }
        cropImageView.setImageBitmap(canvasBitmap);
        destBitmap = canvasBitmap;
        cropImageView.setRotation(90);
    }

    private void formatImage() {
        try {
            float height = picSize.height;
            float width = picSize.width;
            float minScale = Math.min(originHeight / height, originWidth / width);
            if (minScale < 1) {
                Matrix matrix = new Matrix();
                matrix.postScale(1 / minScale, 1 / minScale);
                Bitmap newBitmap = Bitmap.createBitmap(originBitmap, 0, 0, originBitmap.getWidth(), originBitmap.getHeight(), matrix, true);
                String tempPath = ImageUtils.save2Album(newBitmap, Bitmap.CompressFormat.JPEG).getPath();
                originHeight = newBitmap.getHeight();
                originWidth = newBitmap.getWidth();
                BitmapRegionDecoder decoder = BitmapRegionDecoder.newInstance(tempPath, true);
                float scaleWidth = width * minScale;
                float scaleHeight = height * minScale;
                int offsetX = Math.round((originWidth - scaleWidth) / 2);
                int offsetY = Math.round((originHeight - scaleHeight) / 2);
                Rect cropRect = new Rect(0, 0, Math.round(scaleWidth), Math.round(scaleHeight));
                cropRect.offsetTo(offsetX, offsetY);
                destBitmap = decoder.decodeRegion(cropRect, new BitmapFactory.Options());
                cropImageView.setImageBitmap(destBitmap);
            } else {
                BitmapRegionDecoder decoder = BitmapRegionDecoder.newInstance(filePath, true);
                float scaleWidth = width * minScale;
                float scaleHeight = height * minScale;
                int offsetX = Math.round((originWidth - scaleWidth) / 2);
                int offsetY = Math.round((originHeight - scaleHeight) / 2);
                Rect cropRect = new Rect(0, 0, Math.round(scaleWidth), Math.round(scaleHeight));
                cropRect.offsetTo(offsetX, offsetY);
                destBitmap = decoder.decodeRegion(cropRect, new BitmapFactory.Options());
                Matrix matrix = new Matrix();
                matrix.postScale(width / destBitmap.getWidth(), height / destBitmap.getHeight());
                destBitmap = Bitmap.createBitmap(destBitmap, 0, 0, destBitmap.getWidth(), destBitmap.getHeight(), matrix, true);
                cropImageView.setImageBitmap(destBitmap);
                cropImageView.setRotation(0);
            }
            if (saveType == 2) {
                formatMultiImage();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
